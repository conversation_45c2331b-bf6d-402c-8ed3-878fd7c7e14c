
# Arabic Transcript Generation from Audio, Text, and YouTube Videos

This project offers a comprehensive solution for generating Arabic transcripts from various sources, including audio recordings, written text, and YouTube videos. Built with Python and FastAPI, it supports seamless transcription workflows, including real-time status tracking, segmented transcript retrieval, and text-to-dictionary-word conversion for sign language animations. The system also features user sign-up and sign-in functionality, enabling personalized access and session management. Designed with accessibility and linguistic applications in mind, this platform is ideal for use cases such as education, language research, and sign language interpretation.

---

## Prerequisites

- Linux (preferably Ubuntu)
- Python 3.11 or higher
- [FFmpeg](https://ffmpeg.org/download.html)
- Docker
- RabbitMQ
- Celery

---

## Setup Instructions

### Step 1: Clone the Repository

```bash
<NAME_EMAIL>:hirs/arabic-sign-language/asl-arabic-transcript-backend.git
cd asl-arabic-transcript-backend/src
```

---

### Step 2: Install `uv` and Sync Dependencies

This project uses [`uv`](https://docs.astral.sh/uv/) to manage dependencies. Install `uv` by following the instructions in their documentation.

From the `./src/` directory, run:

```bash
uv sync
```

This will create the virtual environment and install all dependencies. Then activate the virtual environment:

```bash
source .venv/bin/activate
```

Make sure your editor is using the correct Python interpreter located at `src/.venv/bin/python`.

---

### Step 3: Modify or Add Code (Optional)

- Define SQLModel models in `./src/app/models.py`
- Add or update API endpoints in `./src/app/api/`
- Add or update CRUD logic in `./src/app/crud.py`

---

### Step 4: Run Alembic Migrations

If you're not using Docker during local development, you can manage Alembic migrations directly using the provided Bash script:

```bash
bash scripts/prestart.sh
```

This script will:

- Wait for the database to start (`app/backend_pre_start.py`)
- Apply any unapplied Alembic migrations
- Populate the database with initial data (`app/initial_data.py`)

If you want to start fresh, you can delete the `.py` files under `./src/app/alembic/versions/` and then create a new migration.

---

### Step 5: Start the FastAPI Application

## VS Code Integration

- Pre-configured to run the backend with the VS Code debugger
- You can use breakpoints, inspect variables, and run tests from the Python test tab
- Start the server with:
  ```bash
  uvicorn app.main:app --reload
  ```

---

## Step 6: Run RabbitMQ (for Celery)

Before starting the Celery worker, ensure RabbitMQ is running. You can quickly start a RabbitMQ container using Docker:

```bash
docker run -it --rm --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
```

### RabbitMQ Ports

- Port 5672: Messaging (used by Celery)
- Port 15672: RabbitMQ Management UI → [http://localhost:15672](http://localhost:15672)

**Default credentials:**

- Username: `guest`
- Password: `guest`

---

### Step 7: Run the Celery Worker

```bash
celery -A app.background_task.celery worker --loglevel=info
```

### Step 8: Add Environment Configuration and Firebase Credentials
To enable Firebase authentication and other configurations:

1. Create a .env file in the `src/` directory and define the required environment variables.


2. Place your `firebase-service-account.json` file in the `src/` directory and ensure your `.env` file includes the correct path.
