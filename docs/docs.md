### Endpoint

POST /api/v1/users/signup

### Description
Register a new user with name, mobile number, email, and password.

### Request
- Method: `POST`
- Content-Type: `application/json`

### Example Request

{
  "name": "test_user",
  "mobile": "9568755512",
  "email": "<EMAIL>",
  "password": "user@123"
}


### Successful Response
Status Code: 201 OK


{
  "message": "Registration successful. You can now log in."
}

### Error Response

400 - Registration failed
{
"status": False,
"detail": "A user with this email already exists."
}

500 - Registration failed
{
  "status": false,
  "detail": "User registration failed"
}




### Endpoint
/api/v1/login/access-token

### Description
OAuth2-compatible token login – Get an access token for future requests

### Request
- Method: `POST`
- Content-Type: `application/x-www-form-urlencoded`


### Example Request
  {
  "grant_type": "password",
  "username": "<EMAIL>",
  "password ": "user_pwd",
  }


### Successful Response

200 – Successful Response
Returns the access token and refresh token upon successful authentication.

{
    "status": true,
    "message": "Login successful",
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer"
}


### Error Response

401 – Unauthorized
Returned when authentication fails due to incorrect credentials.

{
  "status": false,
  "detail": "Incorrect email or password"
}

500 –  Internal Server Error
Returned when the login process fails due to a server error.

{
  "status": false,
  "detail": "Login failed. Please try again later."
}

##
### Endpoint
/api/v1/login/refresh-token

### Description
Generates a new access token using a valid refresh token.
This endpoint ensures continued access without requiring the user to log in again.

### Request
- Method: `POST`
- Content-Type: `application/json`


### Example Request
  {
  "refresh_token": "your_refresh_token_here"
  }



### Successful Response

200 – Successful Response
Returns a new access token upon successful validation of the refresh token.

{
  "status": true,
  "access_token": "new_access_token",
  "token_type": "bearer"
}



### Error Response

401 – Unauthorized

Returned when:

-The refresh token is invalid.

{
  "status": false,
  "detail": "Invalid refresh token"
}
 
-The token type is not "refresh".

{
  "status": false,
  "detail": "Invalid token type"
}

500 – Internal Server Error

Returned for unexpected server-side errors (e.g., issues decoding the token, config problems, etc.).

{
  "status": false,
  "detail": "Something went wrong. Please try again later."
}
