# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set the working directory
WORKDIR /app/ArabicTranscript

# Install Git
RUN apt-get update && \
    apt-get install -y git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Use build arguments for GitHub authentication
ARG GITHUB_USERNAME
ARG GITHUB_TOKEN


# Install git and FFmpeg
RUN apt-get update && \
    apt-get install -y git ffmpeg


# Configure Git with credentials
RUN git config --global credential.helper 'store' && \
    echo "https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com" > ~/.git-credentials

# Clone the repository
RUN git clone https://github.com/kmiyasar-arafath/ArabicTranscript.git . && \
    rm -f ~/.git-credentials

# Copy .env file into the container
COPY .env /app/ArabicTranscript/.env


# Set the working directory to src within the cloned repository
WORKDIR /app/ArabicTranscript/src

# Create a virtual environment
RUN python -m venv /app/ArabicTranscript/.venv

# Install dependencies
RUN . /app/ArabicTranscript/.venv/bin/activate && \
    pip install --upgrade pip && \
    pip install --no-cache-dir -r /app/ArabicTranscript/requirements.txt && \
    pip install numpy==1.26.4


# Set environment variable to use the virtual environment
ENV PATH="/app/ArabicTranscript/.venv/bin:$PATH"

# Expose port 8000 for the Uvicorn server
EXPOSE 8000

# Command to run Uvicorn server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
