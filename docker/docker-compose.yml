services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_USERNAME: ${GITHUB_USERNAME}
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    image: arabic_transcript:latest
    container_name: test_transcript_container
    hostname: "${DOCKER_COMPOSE_HOSTNAME}"
    ports:
      - "8000:8000"
    restart: always
    networks:
      - network

networks:
  network:
    external:
      name: citrus
      