import traceback
import sys
import logging
import inspect
from datetime import datetime, timezone
from datetime import datetime
from sqlmodel import Session
from typing import Optional
from uuid import UUID
def log_exception_to_db(
    *,
    session: Session,
    exc: Exception,
    status_code: int,
    endpoint: str,
    module: str,
    user_id: Optional[UUID] = None,
) -> None:

    from app.models import ExceptionLog, ExceptionLogCreate
    exc_type, exc_value, tb = sys.exc_info()
    stack = traceback.format_exception(exc_type, exc_value, tb)
    last_frame = traceback.extract_tb(tb)[-1] if tb else None
    # print(exc_type.__name__)
    log_data = ExceptionLogCreate(
        exception_type = str(exc_type.__name__),
        message=str(exc),
        stack_trace="".join(stack),
        status_code=status_code,
        module=module,
        endpoint=endpoint,
        file_name=last_frame.filename if last_frame else None,
        line_number=last_frame.lineno if last_frame else None,
        user_id=user_id,
    )
    print(log_data.dict().keys())
    exception_log = ExceptionLog(**log_data.dict())
    try:
        session.add(exception_log)
        session.commit()
    except Exception as log_err:
        session.rollback()
        print("Failed to save exception log:", log_err)
 
try:
    from celery import current_task
except ImportError:
    current_task = None
 
def log_status(logger, level, message):
    frame = inspect.currentframe().f_back
    task_id = None
    worker_name = None
    if current_task and hasattr(current_task, "request"):
        task_id = getattr(current_task.request, "id", None)
        worker_name = getattr(current_task.request, "hostname", None)
    log_record = {
        "level": logging.getLevelName(level),
        "message": message,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "logger": logger.name,
        "module": frame.f_globals["__name__"],
        "function": frame.f_code.co_name,
        "line": frame.f_lineno,
    }
    # Only add Celery info if present
    if task_id or worker_name:
        log_record["task_id"] = task_id
        log_record["worker_name"] = worker_name
    logger.log(level, log_record)