from datetime import datetime, timedelta, timezone
from typing import Any
from jose import JW<PERSON>rror
import jwt
from passlib.context import Crypt<PERSON>ontext
from config import setup_logging
from app.core.config import settings
from app.exceptions import GenericException
from fastapi import status as http_status
import logging

from sqlmodel import Session

logger = setup_logging("security")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


ALGORITHM = "HS256"


# def create_access_token(session: Session,subject: str | Any, expires_delta: timedelta,token_type: str = "access") -> str:
#     from app.exception_log import log_exception_to_db
#     try:
#         expire = datetime.now(timezone.utc) + expires_delta
#         to_encode = {"exp": expire, "sub": str(subject)}
#         print("to_encode:",to_encode)
#         encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
#         return encoded_jwt
#     except JW<PERSON>rror as jwt_err:
#         session.rollback()
#         log_args = {
#         "session": session,
#         "exc": str(jwt_err),
#         "endpoint": "create_access_token",
#         "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
#         "module": __name__,
#         }
#         log_exception_to_db(**log_args) 
#         raise GenericException(
#             status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Failed to generate access token."
#         )
#     except Exception as e:
#         session.rollback()
#         log_args = {
#         "session": session,
#         "exc": str(e),
#         "endpoint": "create_access_token",
#         "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
#         "module": __name__,
#         }
#         log_exception_to_db(**log_args) 
#         raise GenericException(
#             status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Unexpected error occurred during token generation."
        # )

def create_jwt_token(subject: str | Any, expires_delta: timedelta, token_type: str = "access") -> str:
    try:
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode = {
            "exp": expire,
            "sub": str(subject),
            "type": token_type
        }
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except JWTError as jwt_err:
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to encode {token_type} JWT: {str(jwt_err)}"
        )
    except Exception as e:
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during {token_type} token creation: {str(e)}"
        )

def create_tokens(session: Session, user_id: str):
    from app.exception_log import log_exception_to_db, log_status
    try:   
        log_status(logger, logging.INFO, f"Starting token creation for user_id: {user_id}")
        access_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        refresh_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        try:
            access_token = create_jwt_token(subject=user_id, expires_delta=access_expires, token_type="access")
            log_status(logger, logging.INFO, f"Access token created for user_id: {user_id}")
        except Exception as e:
            log_exception_to_db(
                session=session,
                exc=str(e),
                endpoint="create_tokens:access_token",
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                module=__name__
            )
            raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Access token creation failed"
        )
        try:
            refresh_token = create_jwt_token(subject=user_id, expires_delta=refresh_expires, token_type="refresh")
            log_status(logger, logging.INFO, f"Refresh token created for user_id: {user_id}")
        except Exception as e:
            log_exception_to_db(
                session=session,
                exc=str(e),
                endpoint="create_tokens:refresh_token",
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                module=__name__
            )
            raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Refresh token creation failed")

        return access_token, refresh_token
    
    except GenericException as ge:
        raise ge
     
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=str(e),
            endpoint="create_tokens",
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            module=__name__
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error during token generation"
        )

    


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
    


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
