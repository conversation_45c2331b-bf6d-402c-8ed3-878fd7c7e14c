"""
Optimized audio conversion utilities using FFmpeg
"""
import subprocess
import tempfile
import os
import logging
from pathlib import Path
from typing import Union, Optional

from app.core.audio_config import get_ffmpeg_config
from app.exception_log import log_status
from app.api.exceptions.upload_voice_exception import AudioConversionError

logger = logging.getLogger(__name__)


class OptimizedAudioConverter:
    """
    Optimized audio converter with better memory management and performance
    """
    
    def __init__(self):
        self.config = get_ffmpeg_config()
    
    def convert_to_wav_memory(self, input_data: bytes, input_format: str = None) -> bytes:
        """
        Convert audio data to WAV format in memory (for smaller files)
        
        Args:
            input_data: Input audio data as bytes
            input_format: Input format hint (optional)
            
        Returns:
            Converted WAV data as bytes
        """
        try:
            # Build FFmpeg command for memory-to-memory conversion
            cmd = [
                "ffmpeg",
                "-i", "pipe:0",  # Read from stdin
                "-f", self.config["format"],
                "-acodec", self.config["audio_codec"],
                "-ar", self.config["sample_rate"],
                "-ac", self.config["channels"],
            ]
            
            # Add additional optimization arguments
            cmd.extend(self.config["additional_args"])
            cmd.extend([
                "-f", "wav",
                "pipe:1",  # Write to stdout
                "-y"  # Overwrite output
            ])
            
            log_status(logger, logging.INFO, f"Starting FFmpeg conversion with command: {' '.join(cmd)}")
            
            # Execute FFmpeg
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            try:
                output, error = process.communicate(
                    input=input_data, 
                    timeout=self.config["timeout"]
                )
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                raise AudioConversionError(f"FFmpeg conversion timed out after {self.config['timeout']} seconds")
            
            if process.returncode != 0:
                error_msg = error.decode('utf-8', errors='ignore')
                log_status(logger, logging.ERROR, f"FFmpeg conversion failed: {error_msg}")
                raise AudioConversionError(f"FFmpeg conversion failed: {error_msg}")
            
            log_status(logger, logging.INFO, f"FFmpeg conversion successful, output size: {len(output)} bytes")
            return output
            
        except subprocess.TimeoutExpired:
            raise AudioConversionError("FFmpeg conversion timed out")
        except Exception as e:
            log_status(logger, logging.ERROR, f"Audio conversion error: {e}")
            raise AudioConversionError(f"Audio conversion failed: {str(e)}")
    
    def convert_to_wav_file(self, input_path: Union[str, Path], output_path: Union[str, Path]) -> bool:
        """
        Convert audio file to WAV format using file-to-file conversion (for larger files)
        
        Args:
            input_path: Path to input audio file
            output_path: Path to output WAV file
            
        Returns:
            True if conversion successful
        """
        try:
            input_path = Path(input_path)
            output_path = Path(output_path)
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Build FFmpeg command for file-to-file conversion
            cmd = [
                "ffmpeg",
                "-i", str(input_path),
                "-f", self.config["format"],
                "-acodec", self.config["audio_codec"],
                "-ar", self.config["sample_rate"],
                "-ac", self.config["channels"],
            ]
            
            # Add additional optimization arguments
            cmd.extend(self.config["additional_args"])
            cmd.extend([
                str(output_path),
                "-y"  # Overwrite output
            ])
            
            log_status(logger, logging.INFO, f"Starting file conversion: {input_path} -> {output_path}")
            
            # Execute FFmpeg
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            try:
                _, error = process.communicate(timeout=self.config["timeout"])
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                raise AudioConversionError(f"FFmpeg conversion timed out after {self.config['timeout']} seconds")
            
            if process.returncode != 0:
                error_msg = error.decode('utf-8', errors='ignore')
                log_status(logger, logging.ERROR, f"FFmpeg file conversion failed: {error_msg}")
                raise AudioConversionError(f"FFmpeg conversion failed: {error_msg}")
            
            if not output_path.exists():
                raise AudioConversionError("Output file was not created")
            
            log_status(logger, logging.INFO, f"File conversion successful: {output_path}")
            return True
            
        except Exception as e:
            log_status(logger, logging.ERROR, f"File conversion error: {e}")
            raise AudioConversionError(f"File conversion failed: {str(e)}")
    
    def convert_with_temp_file(self, input_data: bytes, session_id: int, temp_dir: str) -> Path:
        """
        Convert audio using temporary files for better memory management
        
        Args:
            input_data: Input audio data
            session_id: Session ID for unique naming
            temp_dir: Temporary directory path
            
        Returns:
            Path to converted WAV file
        """
        input_temp = None
        output_path = None
        
        try:
            # Create temporary input file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
                temp_input.write(input_data)
                input_temp = Path(temp_input.name)
            
            # Create output path
            output_path = Path(temp_dir) / f"{session_id}.wav"
            
            # Convert using file-to-file method
            self.convert_to_wav_file(input_temp, output_path)
            
            return output_path
            
        except Exception as e:
            # Cleanup on error
            if output_path and output_path.exists():
                output_path.unlink()
            raise e
        finally:
            # Cleanup temporary input file
            if input_temp and input_temp.exists():
                try:
                    input_temp.unlink()
                except Exception as cleanup_error:
                    log_status(logger, logging.WARNING, f"Failed to cleanup temp file {input_temp}: {cleanup_error}")


# Global converter instance
audio_converter = OptimizedAudioConverter()


def get_audio_converter() -> OptimizedAudioConverter:
    """Get the global audio converter instance"""
    return audio_converter
