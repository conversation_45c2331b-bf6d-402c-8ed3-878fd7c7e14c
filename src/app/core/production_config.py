"""
Production-ready configuration settings for video transcription service.
"""
import os
from typing import Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings
from app.core.config import Settings


class ProductionSettings(Settings):
    """
    Enhanced settings for production deployment.
    """
    
    # File upload limits
    MAX_FILE_SIZE_MB: int = 100
    MAX_CONCURRENT_UPLOADS: int = 10
    ALLOWED_FILE_EXTENSIONS: list[str] = ['.mp4', '.mov', '.avi', '.mkv', '.wav', '.webm']
    ALLOWED_MIME_TYPES: list[str] = [
        'video/mp4', 'video/quicktime', 'video/x-msvideo', 
        'video/x-matroska', 'video/webm', 'audio/wav'
    ]
    
    # Rate limiting
    UPLOAD_RATE_LIMIT: int = 5  # uploads per hour
    STATUS_RATE_LIMIT: int = 100  # status checks per hour
    CAPTION_RATE_LIMIT: int = 200  # caption fetches per hour
    RATE_LIMIT_WINDOW: int = 3600  # 1 hour in seconds
    
    # Security settings
    ENABLE_CORS: bool = True
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = ["GET", "POST"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]
    
    # Temporary file settings
    TEMP_FILE_CLEANUP_INTERVAL: int = 3600  # 1 hour
    TEMP_FILE_MAX_AGE: int = 7200  # 2 hours
    
    # Background task settings
    CELERY_TASK_TIMEOUT: int = 1800  # 30 minutes
    CELERY_MAX_RETRIES: int = 3
    CELERY_RETRY_DELAY: int = 60  # 1 minute
    
    # Monitoring and logging
    LOG_LEVEL: str = "INFO"
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_INTERVAL: int = 30  # seconds
    
    # External service timeouts
    GOOGLE_API_TIMEOUT: int = 300  # 5 minutes
    FFMPEG_TIMEOUT: int = 300  # 5 minutes
    
    # Database connection pool
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    
    # Redis settings for caching (optional)
    REDIS_URL: Optional[str] = None
    REDIS_CACHE_TTL: int = 3600  # 1 hour
    
    # Content Security Policy
    CSP_HEADER: str = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:; "
        "connect-src 'self'; "
        "font-src 'self'; "
        "object-src 'none'; "
        "media-src 'self'; "
        "frame-src 'none';"
    )
    
    # Security headers
    SECURITY_HEADERS: dict[str, str] = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
    }
    
    @field_validator('MAX_FILE_SIZE_MB')
    @classmethod
    def validate_file_size(cls, v):
        if v <= 0 or v > 500:  # Max 500MB
            raise ValueError('MAX_FILE_SIZE_MB must be between 1 and 500')
        return v

    @field_validator('UPLOAD_RATE_LIMIT')
    @classmethod
    def validate_upload_rate(cls, v):
        if v <= 0 or v > 100:
            raise ValueError('UPLOAD_RATE_LIMIT must be between 1 and 100')
        return v

    @field_validator('LOG_LEVEL')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'LOG_LEVEL must be one of {valid_levels}')
        return v.upper()
    
    @property
    def max_file_size_bytes(self) -> int:
        """Get max file size in bytes."""
        return self.MAX_FILE_SIZE_MB * 1024 * 1024
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT == "local"


# Global production settings instance
production_settings = ProductionSettings()


def get_production_settings() -> ProductionSettings:
    """Get production settings instance."""
    return production_settings


# Environment-specific configurations
def get_cors_settings() -> dict:
    """Get CORS configuration based on environment."""
    if production_settings.is_production:
        return {
            "allow_origins": [production_settings.FRONTEND_HOST],
            "allow_credentials": production_settings.CORS_ALLOW_CREDENTIALS,
            "allow_methods": production_settings.CORS_ALLOW_METHODS,
            "allow_headers": production_settings.CORS_ALLOW_HEADERS,
        }
    else:
        # More permissive for development
        return {
            "allow_origins": ["*"],
            "allow_credentials": False,
            "allow_methods": ["*"],
            "allow_headers": ["*"],
        }


def get_database_settings() -> dict:
    """Get database configuration for production."""
    return {
        "pool_size": production_settings.DB_POOL_SIZE,
        "max_overflow": production_settings.DB_MAX_OVERFLOW,
        "pool_timeout": production_settings.DB_POOL_TIMEOUT,
        "pool_recycle": production_settings.DB_POOL_RECYCLE,
        "echo": not production_settings.is_production,  # Disable SQL logging in production
    }


def get_celery_settings() -> dict:
    """Get Celery configuration for production."""
    return {
        "task_soft_time_limit": production_settings.CELERY_TASK_TIMEOUT,
        "task_time_limit": production_settings.CELERY_TASK_TIMEOUT + 60,
        "task_max_retries": production_settings.CELERY_MAX_RETRIES,
        "task_default_retry_delay": production_settings.CELERY_RETRY_DELAY,
        "worker_prefetch_multiplier": 1,  # Prevent memory issues
        "task_acks_late": True,  # Ensure task completion
        "worker_disable_rate_limits": False,
        "task_reject_on_worker_lost": True,
    }
