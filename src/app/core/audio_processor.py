"""
Memory-optimized audio processing utilities
"""
import logging
import gc
from pathlib import Path
from typing import Generator, Tuple, List, Optional
import torch
import numpy as np
from silero_vad import read_audio

from app.core.audio_config import get_vad_config, get_memory_config
from app.core.model_manager import get_model_manager
from app.exception_log import log_status

logger = logging.getLogger(__name__)


class MemoryOptimizedAudioProcessor:
    """
    Memory-optimized audio processor with streaming capabilities
    """
    
    def __init__(self):
        self.vad_config = get_vad_config()
        self.memory_config = get_memory_config()
        self.model_manager = get_model_manager()
        
    def load_audio_optimized(self, audio_path: str, sampling_rate: int = 16000) -> torch.Tensor:
        """
        Load audio with memory optimization
        
        Args:
            audio_path: Path to audio file
            sampling_rate: Target sampling rate
            
        Returns:
            Audio tensor
        """
        try:
            log_status(logger, logging.INFO, f"Loading audio from {audio_path}")
            
            # Check file size and decide on loading strategy
            file_size = Path(audio_path).stat().st_size
            max_size = self.memory_config["chunk_size"] * 1000  # Rough threshold
            
            if file_size > max_size:
                log_status(logger, logging.INFO, f"Large file detected ({file_size} bytes), using optimized loading")
                return self._load_audio_chunked(audio_path, sampling_rate)
            else:
                log_status(logger, logging.INFO, f"Small file detected ({file_size} bytes), using standard loading")
                return read_audio(audio_path, sampling_rate=sampling_rate)
                
        except Exception as e:
            log_status(logger, logging.ERROR, f"Failed to load audio: {e}")
            raise
    
    def _load_audio_chunked(self, audio_path: str, sampling_rate: int) -> torch.Tensor:
        """
        Load large audio files in chunks to manage memory
        """
        # For now, use standard loading but with explicit memory management
        # In production, you might want to implement actual chunked loading
        wav = read_audio(audio_path, sampling_rate=sampling_rate)
        
        # Validate audio length
        max_length = self.memory_config["max_audio_length_seconds"] * sampling_rate
        if len(wav) > max_length:
            log_status(logger, logging.WARNING, 
                      f"Audio length ({len(wav)} samples) exceeds maximum ({max_length}), truncating")
            wav = wav[:max_length]
        
        return wav
    
    def process_audio_chunks(self, wav: torch.Tensor, sampling_rate: int = 16000) -> Generator[Tuple[int, dict], None, None]:
        """
        Process audio in optimized chunks with memory management
        
        Args:
            wav: Audio tensor
            sampling_rate: Sampling rate
            
        Yields:
            Tuple of (chunk_index, speech_detection_result)
        """
        window_size_samples = self.vad_config["window_size_samples"]
        cleanup_interval = self.memory_config["tensor_cleanup_interval"]
        
        # Get VAD iterator from model manager
        vad_iterator = self.model_manager.get_vad_iterator(
            threshold=self.vad_config["threshold"],
            sampling_rate=sampling_rate
        )
        
        global_tensor = torch.empty((0,))
        chunk_count = 0
        
        try:
            for i in range(0, len(wav), window_size_samples):
                chunk = wav[i : i + window_size_samples]
                
                # Check if this is the final chunk
                is_final_chunk = (len(chunk) < window_size_samples or 
                                (i + window_size_samples) >= len(wav))
                
                if is_final_chunk:
                    log_status(logger, logging.INFO, f"Processing final chunk at index {i}")
                
                # Run VAD on chunk
                try:
                    speech_dict = vad_iterator(chunk, return_seconds=True)
                    
                    # Concatenate to global tensor
                    global_tensor = torch.cat((global_tensor, chunk))
                    
                    # Yield result
                    yield i, speech_dict, global_tensor, is_final_chunk
                    
                    # Periodic memory cleanup
                    chunk_count += 1
                    if chunk_count % cleanup_interval == 0:
                        self._cleanup_memory()
                    
                    if is_final_chunk:
                        break
                        
                except Exception as chunk_error:
                    log_status(logger, logging.ERROR, f"Error processing chunk {i}: {chunk_error}")
                    yield i, None, global_tensor, is_final_chunk
                    
                    if is_final_chunk:
                        break
                        
        finally:
            # Final cleanup
            self._cleanup_memory()
            
            # Reset VAD states
            self.model_manager.reset_vad_states()
    
    def _cleanup_memory(self):
        """Perform memory cleanup"""
        try:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except Exception as e:
            log_status(logger, logging.WARNING, f"Memory cleanup warning: {e}")
    
    def extract_speech_segments(self, speech_dict: dict, global_tensor: torch.Tensor, 
                              chunk_index: int) -> List[Tuple[float, float, torch.Tensor]]:
        """
        Extract speech segments from detection results
        
        Args:
            speech_dict: Speech detection results
            global_tensor: Accumulated audio tensor
            chunk_index: Current chunk index
            
        Returns:
            List of (start_time, end_time, audio_segment) tuples
        """
        segments = []
        
        if speech_dict is None:
            return segments
        
        # Check if we have complete speech segments (with both start and end)
        if not any("end" in str(key) for key in speech_dict.keys()):
            return segments
        
        try:
            # Extract speech segments
            for key, value in speech_dict.items():
                if isinstance(value, dict) and "start" in value and "end" in value:
                    start_time = value["start"]
                    end_time = value["end"]
                    
                    # Add padding around speech segments
                    pad_seconds = self.vad_config["speech_pad_ms"] / 1000.0
                    start_time = max(0, start_time - pad_seconds)
                    end_time = end_time + pad_seconds
                    
                    segments.append((start_time, end_time, global_tensor))
                    
        except Exception as e:
            log_status(logger, logging.ERROR, f"Error extracting speech segments: {e}")
        
        return segments
    
    def validate_audio_quality(self, wav: torch.Tensor, sampling_rate: int = 16000) -> bool:
        """
        Validate audio quality and characteristics
        
        Args:
            wav: Audio tensor
            sampling_rate: Sampling rate
            
        Returns:
            True if audio quality is acceptable
        """
        try:
            # Check audio length
            duration_seconds = len(wav) / sampling_rate
            if duration_seconds < 0.1:  # Too short
                log_status(logger, logging.WARNING, f"Audio too short: {duration_seconds:.2f}s")
                return False
            
            if duration_seconds > self.memory_config["max_audio_length_seconds"]:
                log_status(logger, logging.WARNING, f"Audio too long: {duration_seconds:.2f}s")
                return False
            
            # Check for silence (all zeros or very low amplitude)
            max_amplitude = torch.max(torch.abs(wav))
            if max_amplitude < 0.001:  # Essentially silent
                log_status(logger, logging.WARNING, f"Audio appears to be silent (max amplitude: {max_amplitude})")
                return False
            
            log_status(logger, logging.INFO, 
                      f"Audio validation passed: duration={duration_seconds:.2f}s, max_amplitude={max_amplitude:.4f}")
            return True
            
        except Exception as e:
            log_status(logger, logging.ERROR, f"Audio validation error: {e}")
            return False


# Global processor instance
audio_processor = MemoryOptimizedAudioProcessor()


def get_audio_processor() -> MemoryOptimizedAudioProcessor:
    """Get the global audio processor instance"""
    return audio_processor
