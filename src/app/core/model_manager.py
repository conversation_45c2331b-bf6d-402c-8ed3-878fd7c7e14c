"""
Model Manager for preloading and managing ML models
"""
import logging
from typing import Optional
import torch
import gc
from silero_vad import load_silero_vad, VADIterator

from app.exception_log import log_status
from config import setup_logging

logger = setup_logging("model_manager")


class ModelManager:
    """
    Singleton class to manage ML models and avoid reloading them for each task.
    """
    _instance: Optional['ModelManager'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModelManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.vad_model = None
            self.vad_iterator = None
            self._sampling_rate = 16000
            self._vad_threshold = 0.3
            ModelManager._initialized = True
    
    def load_models(self):
        """Load all required models at startup with detailed logging"""
        import time
        import psutil
        import os

        start_time = time.time()
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        try:
            log_status(logger, logging.INFO,
                      f"Starting model loading process - PID: {os.getpid()}, "
                      f"Initial memory: {initial_memory:.1f}MB")

            # Load VAD model with timing
            vad_start = time.time()
            log_status(logger, logging.INFO, "Loading Silero VAD model (ONNX)...")
            self.vad_model = load_silero_vad(onnx=True)
            vad_load_time = time.time() - vad_start
            vad_memory = process.memory_info().rss / 1024 / 1024  # MB

            log_status(logger, logging.INFO,
                      f"Silero VAD model loaded successfully in {vad_load_time:.3f}s, "
                      f"Memory after VAD: {vad_memory:.1f}MB (+{vad_memory-initial_memory:.1f}MB)")

            # Pre-create VAD iterator with default settings
            iterator_start = time.time()
            log_status(logger, logging.INFO, " Creating VAD iterator with default settings...")
            self.create_vad_iterator()
            iterator_time = time.time() - iterator_start

            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            total_time = time.time() - start_time

            log_status(logger, logging.INFO,
                      f"Model loading completed successfully! "
                      f"Total time: {total_time:.3f}s, "
                      f"Final memory: {final_memory:.1f}MB (+{final_memory-initial_memory:.1f}MB), "
                      f"VAD iterator ready in {iterator_time:.3f}s")

        except Exception as e:
            error_time = time.time() - start_time
            error_memory = process.memory_info().rss / 1024 / 1024  # MB
            log_status(logger, logging.ERROR,
                      f"Failed to load models after {error_time:.3f}s: {e}, "
                      f"Memory at error: {error_memory:.1f}MB")
            raise
    
    def create_vad_iterator(self, threshold: float = None, sampling_rate: int = None):
        """Create or recreate VAD iterator with specified parameters and detailed logging"""
        import time

        threshold = threshold or self._vad_threshold
        sampling_rate = sampling_rate or self._sampling_rate

        start_time = time.time()

        try:
            if self.vad_model is None:
                raise RuntimeError("VAD model not loaded. Call load_models() first.")

            log_status(logger, logging.INFO,
                      f" Creating VAD iterator - threshold: {threshold}, sampling_rate: {sampling_rate}Hz")

            # Reset existing iterator if parameters changed
            if (self.vad_iterator is not None and
                (threshold != self._vad_threshold or sampling_rate != self._sampling_rate)):
                log_status(logger, logging.INFO, "Resetting existing VAD iterator (parameters changed)")
                self.vad_iterator.reset_states()
                del self.vad_iterator
                gc.collect()

            # Create new iterator
            iterator_start = time.time()
            self.vad_iterator = VADIterator(
                self.vad_model,
                sampling_rate=sampling_rate,
                threshold=threshold
            )
            iterator_creation_time = time.time() - iterator_start

            self._vad_threshold = threshold
            self._sampling_rate = sampling_rate

            total_time = time.time() - start_time
            log_status(logger, logging.INFO,
                      f" VAD Iterator created successfully in {total_time:.3f}s "
                      f"(iterator: {iterator_creation_time:.3f}s) - "
                      f"threshold={threshold}, sampling_rate={sampling_rate}Hz")

        except Exception as e:
            error_time = time.time() - start_time
            log_status(logger, logging.ERROR,
                      f" Failed to create VAD iterator after {error_time:.3f}s: {e}")
            raise

    def is_model_loaded(self) -> bool:
        """Check if VAD model is loaded with logging"""
        is_loaded = self.vad_model is not None
        log_status(logger, logging.DEBUG, f" Model loaded status check: {is_loaded}")
        return is_loaded

    def get_vad_model(self):
        """Get the preloaded VAD model with detailed logging"""
        if self.vad_model is None:
            log_status(logger, logging.WARNING, " VAD model not loaded, loading now...")
            self.load_models()
            log_status(logger, logging.INFO, " VAD model loaded on-demand")
        else:
            log_status(logger, logging.DEBUG, " Using pre-loaded VAD model")
        return self.vad_model
    
    def get_vad_iterator(self, threshold: float = None, sampling_rate: int = None):
        """Get VAD iterator, creating new one if parameters changed, with detailed logging"""
        threshold = threshold or self._vad_threshold
        sampling_rate = sampling_rate or self._sampling_rate

        log_status(logger, logging.DEBUG,
                  f"VAD iterator requested - threshold: {threshold}, sampling_rate: {sampling_rate}Hz")

        # Ensure VAD model is loaded first
        if self.vad_model is None:
            log_status(logger, logging.WARNING, "VAD model not loaded, loading now...")
            self.load_models()

        # Check if we need to create/recreate iterator
        needs_creation = (
            self.vad_iterator is None or
            threshold != self._vad_threshold or
            sampling_rate != self._sampling_rate
        )

        if needs_creation:
            if self.vad_iterator is None:
                log_status(logger, logging.INFO, "Creating new VAD iterator (none exists)")
            else:
                log_status(logger, logging.INFO,
                          f"Recreating VAD iterator (parameters changed: "
                          f"threshold {self._vad_threshold}→{threshold}, "
                          f"sampling_rate {self._sampling_rate}→{sampling_rate})")

            self.create_vad_iterator(threshold, sampling_rate)
        else:
            log_status(logger, logging.DEBUG, "Using existing VAD iterator (parameters match)")

        return self.vad_iterator
    
    def reset_vad_states(self):
        """Reset VAD iterator states"""
        if self.vad_iterator:
            self.vad_iterator.reset_states()
    
    def cleanup(self):
        """Cleanup models and free memory with detailed logging"""
        import time
        import psutil
        import os

        start_time = time.time()
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        try:
            log_status(logger, logging.INFO,
                      f"Starting model cleanup - PID: {os.getpid()}, "
                      f"Initial memory: {initial_memory:.1f}MB")

            cleanup_items = []

            if self.vad_iterator:
                log_status(logger, logging.INFO, "Resetting and cleaning VAD iterator...")
                self.vad_iterator.reset_states()
                del self.vad_iterator
                self.vad_iterator = None
                cleanup_items.append("VAD iterator")

            if self.vad_model:
                log_status(logger, logging.INFO, "Cleaning VAD model...")
                del self.vad_model
                self.vad_model = None
                cleanup_items.append("VAD model")

            # Force garbage collection
            log_status(logger, logging.INFO, "Running garbage collection...")
            gc.collect()

            # Clear CUDA cache if available
            if torch.cuda.is_available():
                log_status(logger, logging.INFO, "Clearing CUDA cache...")
                torch.cuda.empty_cache()
                cleanup_items.append("CUDA cache")

            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            cleanup_time = time.time() - start_time
            memory_freed = initial_memory - final_memory

            log_status(logger, logging.INFO,
                      f"Model cleanup completed in {cleanup_time:.3f}s - "
                      f"Cleaned: {', '.join(cleanup_items) if cleanup_items else 'nothing to clean'}, "
                      f"Memory: {initial_memory:.1f}MB → {final_memory:.1f}MB "
                      f"({'freed' if memory_freed > 0 else 'used'} {abs(memory_freed):.1f}MB)")

        except Exception as e:
            error_time = time.time() - start_time
            error_memory = process.memory_info().rss / 1024 / 1024  # MB
            log_status(logger, logging.ERROR,
                      f"Error during model cleanup after {error_time:.3f}s: {e}, "
                      f"Memory at error: {error_memory:.1f}MB")


# Global model manager instance
model_manager = ModelManager()


def initialize_models():
    """Initialize models at application startup with comprehensive logging"""
    import time
    import os

    start_time = time.time()
    pid = os.getpid()

    log_status(logger, logging.INFO,
              f"Starting model initialization - PID: {pid}")

    try:
        model_manager.load_models()

        # Verify models are loaded
        if model_manager.is_model_loaded():
            total_time = time.time() - start_time
            log_status(logger, logging.INFO,
                      f"Model initialization completed successfully in {total_time:.3f}s - "
                      f"PID: {pid}, Models ready for use!")
        else:
            log_status(logger, logging.ERROR,
                      f"Model initialization failed - models not loaded after completion")

    except Exception as e:
        error_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Model initialization failed after {error_time:.3f}s - PID: {pid}, Error: {e}")
        raise


def get_model_manager() -> ModelManager:
    """Get the global model manager instance"""
    return model_manager
