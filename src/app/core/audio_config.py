"""
Audio processing configuration and optimization settings
"""

# Audio processing configuration
AUDIO_CONFIG = {
    # Sampling configuration
    "sampling_rate": 16000,
    "target_channels": 1,  # Mono
    
    # VAD configuration - optimized for better performance
    "vad": {
        "threshold": 0.3,
        "min_speech_duration_ms": 250,
        "max_speech_duration_s": 30,
        "min_silence_duration_ms": 100,
        "window_size_samples": 512,  # Increased from 512 for better performance
        "speech_pad_ms": 30,  # Padding around speech segments
    },
    
    # FFmpeg optimization parameters
    "ffmpeg": {
        "audio_codec": "pcm_s16le",  # Specific codec for better performance
        "format": "wav",
        "sample_rate": "16000",
        "channels": "1",
        "timeout": 120,  # Increased timeout for larger files
        "additional_args": [
            "-avoid_negative_ts", "make_zero",  # Handle timestamp issues
            "-fflags", "+genpts",  # Generate presentation timestamps
        ]
    },
    
    # Memory management
    "memory": {
        "chunk_size": 8192,  # Bytes to read at a time for streaming
        "max_audio_length_seconds": 3600,  # 1 hour max
        "tensor_cleanup_interval": 100,  # Clean up every N chunks
    },
    
    # Processing optimization
    "processing": {
        "batch_size": 10,  # Process multiple chunks together
        "parallel_workers": 2,  # Number of parallel processing workers
        "enable_streaming": True,  # Enable streaming processing
    }
}


def get_audio_config():
    """Get the audio configuration"""
    return AUDIO_CONFIG


def get_vad_config(sampling_rate: int = None):
    """
    Get VAD-specific configuration with dynamic window size

    Args:
        sampling_rate: Audio sampling rate in Hz. If None, uses default from config.

    Returns:
        dict: VAD configuration with appropriate window size for sampling rate
    """
    vad_config = AUDIO_CONFIG["vad"].copy()

    # Use provided sampling rate or default
    if sampling_rate is None:
        sampling_rate = AUDIO_CONFIG["sampling_rate"]

    # Dynamic window size based on sampling rate (Silero VAD requirements)
    # 256 samples for 8000 Hz, 512 samples for 16000 Hz
    vad_config["window_size_samples"] = 512 if sampling_rate == 16000 else 256
    vad_config["sampling_rate"] = sampling_rate

    return vad_config


def get_ffmpeg_config():
    """Get FFmpeg-specific configuration"""
    return AUDIO_CONFIG["ffmpeg"]


def get_memory_config():
    """Get memory management configuration"""
    return AUDIO_CONFIG["memory"]


def get_processing_config():
    """Get processing optimization configuration"""
    return AUDIO_CONFIG["processing"]
