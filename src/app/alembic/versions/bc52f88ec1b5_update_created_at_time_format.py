"""Update created_at time format

Revision ID: bc52f88ec1b5
Revises: 0a0bbc3fee40
Create Date: 2025-07-04 11:31:07.430734

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bc52f88ec1b5'
down_revision = '0a0bbc3fee40'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('auth_credentials', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('exceptionlog', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
    op.alter_column('transcriptions', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.add_column('user_tenants', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('user_tenants', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('user_tenants', sa.Column('created_by', sa.Uuid(), nullable=True))
    op.add_column('user_tenants', sa.Column('updated_by', sa.Uuid(), nullable=True))
    op.add_column('user_tenants', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('user_tenants', sa.Column('deleted_by', sa.Uuid(), nullable=True))
    op.drop_index('ix_users_email', table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.alter_column('videos', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('videos', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index('ix_users_email', 'users', ['email'], unique=False)
    op.drop_column('user_tenants', 'deleted_by')
    op.drop_column('user_tenants', 'deleted_at')
    op.drop_column('user_tenants', 'updated_by')
    op.drop_column('user_tenants', 'created_by')
    op.drop_column('user_tenants', 'updated_at')
    op.drop_column('user_tenants', 'created_at')
    op.alter_column('transcriptions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('exceptionlog', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False)
    op.alter_column('auth_credentials', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=False,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###
