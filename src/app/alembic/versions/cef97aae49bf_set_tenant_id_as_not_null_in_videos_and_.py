"""Set tenant_id as NOT NULL in videos and transcription

Revision ID: cef97aae49bf
Revises: bc52f88ec1b5
Create Date: 2025-07-07 09:54:32.093913

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cef97aae49bf'
down_revision = 'bc52f88ec1b5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transcriptions', 'tenant_id', existing_type=sa.Uuid(), nullable=False)
    op.alter_column('videos', 'tenant_id', existing_type=sa.Uuid(), nullable=False)

    # op.create_table('audios',
    # sa.Column('id', sa.Integer(), nullable=False),
    # sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    # sa.Column('status', sa.Enum('IN_PROGRESS', 'COMPLETED', 'ERROR', 'QUEUED', name='statusenum'), nullable=False),
    # sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    # sa.Column('updated_at', sa.DateTime(), nullable=False),
    # sa.Column('created_by', sa.Uuid(), nullable=True),
    # sa.Column('updated_by', sa.Uuid(), nullable=True),
    # sa.Column('deleted_at', sa.DateTime(), nullable=True),
    # sa.Column('deleted_by', sa.Uuid(), nullable=True),
    # sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    # sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    # sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    # sa.PrimaryKeyConstraint('id')
    # )
    # op.create_index(op.f('ix_audios_created_by'), 'audios', ['created_by'], unique=False)
    # op.create_index(op.f('ix_audios_id'), 'audios', ['id'], unique=False)
    # op.create_table('audio_transcriptions',
    # sa.Column('id', sa.Integer(), nullable=False),
    # sa.Column('audio_id', sa.Integer(), nullable=False),
    # sa.Column('start_time', sa.Float(), nullable=False),
    # sa.Column('end_time', sa.Float(), nullable=False),
    # sa.Column('word', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    # sa.Column('root', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    # sa.Column('chunk', sa.Integer(), nullable=False),
    # sa.Column('status', sa.Enum('IN_PROGRESS', 'COMPLETED', 'ERROR', 'QUEUED', name='statusenum'), nullable=False),
    # sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    # sa.Column('updated_at', sa.DateTime(), nullable=True),
    # sa.Column('created_by', sa.Uuid(), nullable=True),
    # sa.Column('updated_by', sa.Uuid(), nullable=True),
    # sa.Column('deleted_at', sa.DateTime(), nullable=True),
    # sa.Column('deleted_by', sa.Uuid(), nullable=True),
    # sa.ForeignKeyConstraint(['audio_id'], ['audios.id'], ),
    # sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    # sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    # sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    # sa.PrimaryKeyConstraint('id')
    # )
    # op.create_index(op.f('ix_audio_transcriptions_audio_id'), 'audio_transcriptions', ['audio_id'], unique=False)
    # op.create_index(op.f('ix_audio_transcriptions_created_by'), 'audio_transcriptions', ['created_by'], unique=False)
    # op.create_index(op.f('ix_audio_transcriptions_id'), 'audio_transcriptions', ['id'], unique=False)
    # op.drop_table('celery_taskmeta')
    # op.drop_table('celery_tasksetmeta')
    # op.alter_column('transcriptions', 'tenant_id',
    #            existing_type=sa.UUID(),
    #            nullable=False)
    # op.alter_column('videos', 'tenant_id',
    #            existing_type=sa.UUID(),
    #            nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transcriptions', 'tenant_id', existing_type=sa.Uuid(), nullable=True)
    op.alter_column('videos', 'tenant_id', existing_type=sa.Uuid(), nullable=True)
    # op.alter_column('videos', 'tenant_id',
    #            existing_type=sa.UUID(),
    #            nullable=True)
    # op.alter_column('transcriptions', 'tenant_id',
    #            existing_type=sa.UUID(),
    #            nullable=True)
    # op.create_table('celery_tasksetmeta',
    # sa.Column('id', sa.INTEGER(), autoincrement=False, nullable=False),
    # sa.Column('taskset_id', sa.VARCHAR(length=155), autoincrement=False, nullable=True),
    # sa.Column('result', postgresql.BYTEA(), autoincrement=False, nullable=True),
    # sa.Column('date_done', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    # sa.PrimaryKeyConstraint('id', name='celery_tasksetmeta_pkey'),
    # sa.UniqueConstraint('taskset_id', name='celery_tasksetmeta_taskset_id_key')
    # )
    # op.create_table('celery_taskmeta',
    # sa.Column('id', sa.INTEGER(), autoincrement=False, nullable=False),
    # sa.Column('task_id', sa.VARCHAR(length=155), autoincrement=False, nullable=True),
    # sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    # sa.Column('result', postgresql.BYTEA(), autoincrement=False, nullable=True),
    # sa.Column('date_done', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    # sa.Column('traceback', sa.TEXT(), autoincrement=False, nullable=True),
    # sa.Column('name', sa.VARCHAR(length=155), autoincrement=False, nullable=True),
    # sa.Column('args', postgresql.BYTEA(), autoincrement=False, nullable=True),
    # sa.Column('kwargs', postgresql.BYTEA(), autoincrement=False, nullable=True),
    # sa.Column('worker', sa.VARCHAR(length=155), autoincrement=False, nullable=True),
    # sa.Column('retries', sa.INTEGER(), autoincrement=False, nullable=True),
    # sa.Column('queue', sa.VARCHAR(length=155), autoincrement=False, nullable=True),
    # sa.PrimaryKeyConstraint('id', name='celery_taskmeta_pkey'),
    # sa.UniqueConstraint('task_id', name='celery_taskmeta_task_id_key')
    # )
    # op.drop_index(op.f('ix_audio_transcriptions_id'), table_name='audio_transcriptions')
    # op.drop_index(op.f('ix_audio_transcriptions_created_by'), table_name='audio_transcriptions')
    # op.drop_index(op.f('ix_audio_transcriptions_audio_id'), table_name='audio_transcriptions')
    # op.drop_table('audio_transcriptions')
    # op.drop_index(op.f('ix_audios_id'), table_name='audios')
    # op.drop_index(op.f('ix_audios_created_by'), table_name='audios')
    # op.drop_table('audios')
    # ### end Alembic commands ###
