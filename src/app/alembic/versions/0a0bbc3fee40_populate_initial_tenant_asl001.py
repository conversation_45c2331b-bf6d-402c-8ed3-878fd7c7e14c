"""Populate initial tenant ASL001

Revision ID: 0a0bbc3fee40
Revises: a04fada8451e
Create Date: 2025-07-03 11:14:34.236727

"""
from alembic import op
import sqlalchemy as sa
import uuid
from datetime import datetime, timezone

# revision identifiers, used by Alembic.
revision = '0a0bbc3fee40'
down_revision = 'a04fada8451e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    tenant_id = uuid.uuid4()
    created_at = datetime.utcnow()

    insert_stmt = sa.text("""
        INSERT INTO tenants (id, org_name, tenant_code, is_active, email, phone, address, created_at)
        VALUES (:id, :org_name, :tenant_code, :is_active, :email, :phone, :address, :created_at)
    """).bindparams(
        id=tenant_id,
        org_name='ASL Solutions Inc',
        tenant_code='ASL001',
        is_active=True,
        email='<EMAIL>',
        phone='9856234586',
        address='123 Business St, NY 10001',
        created_at=created_at
    )

    op.execute(insert_stmt)


def downgrade():
    pass
