"""Update table structure for audit fields

Revision ID: fbe33cc01502
Revises: bdb5a1fef90b
Create Date: 2025-06-25 10:33:52.285346

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql
import datetime

# revision identifiers, used by Alembic.
revision = 'fbe33cc01502'
down_revision = 'bdb5a1fef90b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_testtable_id'), table_name='testtable')
    op.drop_table('testtable')
    op.add_column('transcriptions', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('transcriptions', sa.Column('created_by', sa.Uuid(), nullable=True))
    op.add_column('transcriptions', sa.Column('updated_by', sa.Uuid(), nullable=True))
    op.add_column('transcriptions', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('transcriptions', sa.Column('deleted_by', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_transcriptions_created_by'), 'transcriptions', ['created_by'], unique=False)
    op.create_foreign_key(None, 'transcriptions', 'users', ['deleted_by'], ['id'])
    op.create_foreign_key(None, 'transcriptions', 'users', ['updated_by'], ['id'])
    op.create_foreign_key(None, 'transcriptions', 'users', ['created_by'], ['id'])
    op.drop_column('transcriptions', 'modified_at')
    op.add_column('users', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('deleted_by', sa.Uuid(), nullable=True))
    op.create_foreign_key(None, 'users', 'users', ['deleted_by'], ['id'])
    op.add_column('videos', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.execute(
        f"UPDATE videos SET updated_at = '{datetime.datetime.utcnow()}'"
    )
    op.alter_column('videos', 'updated_at', nullable=False)
    op.add_column('videos', sa.Column('created_by', sa.Uuid(), nullable=True))
    op.add_column('videos', sa.Column('updated_by', sa.Uuid(), nullable=True))
    op.add_column('videos', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('videos', sa.Column('deleted_by', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_videos_created_by'), 'videos', ['created_by'], unique=False)
    op.create_foreign_key(None, 'videos', 'users', ['deleted_by'], ['id'])
    op.create_foreign_key(None, 'videos', 'users', ['updated_by'], ['id'])
    op.create_foreign_key(None, 'videos', 'users', ['created_by'], ['id'])
    op.drop_column('videos', 'modified_at')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('videos', sa.Column('modified_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'videos', type_='foreignkey')
    op.drop_constraint(None, 'videos', type_='foreignkey')
    op.drop_constraint(None, 'videos', type_='foreignkey')
    op.drop_index(op.f('ix_videos_created_by'), table_name='videos')
    op.drop_column('videos', 'deleted_by')
    op.drop_column('videos', 'deleted_at')
    op.drop_column('videos', 'updated_by')
    op.drop_column('videos', 'created_by')
    op.drop_column('videos', 'updated_at')
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'deleted_by')
    op.drop_column('users', 'deleted_at')
    op.add_column('transcriptions', sa.Column('modified_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'transcriptions', type_='foreignkey')
    op.drop_constraint(None, 'transcriptions', type_='foreignkey')
    op.drop_constraint(None, 'transcriptions', type_='foreignkey')
    op.drop_index(op.f('ix_transcriptions_created_by'), table_name='transcriptions')
    op.drop_column('transcriptions', 'deleted_by')
    op.drop_column('transcriptions', 'deleted_at')
    op.drop_column('transcriptions', 'updated_by')
    op.drop_column('transcriptions', 'created_by')
    op.drop_column('transcriptions', 'updated_at')
    op.create_table('testtable',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('testtable_pkey'))
    )
    op.create_index(op.f('ix_testtable_id'), 'testtable', ['id'], unique=False)
    # ### end Alembic commands ###
