"""remove sample table

Revision ID: e07741664f0e
Revises: 5c4e1aaeb2f0
Create Date: 2025-06-25 11:14:04.361768

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'e07741664f0e'
down_revision = '5c4e1aaeb2f0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sample')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sample',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('value', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('place', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('age', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('sample_pkey'))
    )
    # ### end Alembic commands ###
