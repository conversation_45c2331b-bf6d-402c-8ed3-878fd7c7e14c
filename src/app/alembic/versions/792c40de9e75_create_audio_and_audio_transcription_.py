"""create audio and audio transcription table

Revision ID: 792c40de9e75
Revises: 0ae44cedb050
Create Date: 2025-07-09 16:30:14.588646

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '792c40de9e75'
down_revision = '0ae44cedb050'
branch_labels = None
depends_on = None


def upgrade():

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audios',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', sa.Enum('IN_PROGRESS', 'COMPLETED', 'ERROR', 'QUEUED', 'UPLOADED', 'UPLOADING', name='statusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('updated_by', sa.Uuid(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_by', sa.Uuid(), nullable=True),
    sa.Column('tenant_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_audios_created_by'), 'audios', ['created_by'], unique=False)
    op.create_index(op.f('ix_audios_id'), 'audios', ['id'], unique=False)
    op.create_index(op.f('ix_audios_tenant_id'), 'audios', ['tenant_id'], unique=False)
    op.create_table('audiotranscriptions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('audio_id', sa.Integer(), nullable=False),
    sa.Column('start_time', sa.Float(), nullable=False),
    sa.Column('end_time', sa.Float(), nullable=False),
    sa.Column('word', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('root', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('chunk', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('IN_PROGRESS', 'COMPLETED', 'ERROR', 'QUEUED', 'UPLOADED', 'UPLOADING', name='statusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('updated_by', sa.Uuid(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_by', sa.Uuid(), nullable=True),
    sa.Column('tenant_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['audio_id'], ['audios.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_audiotranscriptions_audio_id'), 'audiotranscriptions', ['audio_id'], unique=False)
    op.create_index(op.f('ix_audiotranscriptions_created_by'), 'audiotranscriptions', ['created_by'], unique=False)
    op.create_index(op.f('ix_audiotranscriptions_id'), 'audiotranscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_audiotranscriptions_tenant_id'), 'audiotranscriptions', ['tenant_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_audiotranscriptions_tenant_id'), table_name='audiotranscriptions')
    op.drop_index(op.f('ix_audiotranscriptions_id'), table_name='audiotranscriptions')
    op.drop_index(op.f('ix_audiotranscriptions_created_by'), table_name='audiotranscriptions')
    op.drop_index(op.f('ix_audiotranscriptions_audio_id'), table_name='audiotranscriptions')
    op.drop_table('audiotranscriptions')
    op.drop_index(op.f('ix_audios_tenant_id'), table_name='audios')
    op.drop_index(op.f('ix_audios_id'), table_name='audios')
    op.drop_index(op.f('ix_audios_created_by'), table_name='audios')
    op.drop_table('audios')
    # ### end Alembic commands ###
