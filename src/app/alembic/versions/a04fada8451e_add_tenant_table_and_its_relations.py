"""Add Tenant table and its relations

Revision ID: a04fada8451e
Revises: e07741664f0e
Create Date: 2025-07-02 17:49:25.013510

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a04fada8451e'
down_revision = 'e07741664f0e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenants',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('org_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('tenant_code', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('phone', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=True),
    sa.Column('address', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Uuid(), nullable=True),
    sa.Column('updated_by', sa.Uuid(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_by', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenants_org_name'), 'tenants', ['org_name'], unique=False)
    op.create_index(op.f('ix_tenants_tenant_code'), 'tenants', ['tenant_code'], unique=True)
    op.create_table('user_tenants',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('tenant_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('transcriptions', sa.Column('tenant_id', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_transcriptions_tenant_id'), 'transcriptions', ['tenant_id'], unique=False)
    op.create_foreign_key(None, 'transcriptions', 'tenants', ['tenant_id'], ['id'])
    op.add_column('videos', sa.Column('tenant_id', sa.Uuid(), nullable=True))
    op.create_index(op.f('ix_videos_tenant_id'), 'videos', ['tenant_id'], unique=False)
    op.create_foreign_key(None, 'videos', 'tenants', ['tenant_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'videos', type_='foreignkey')
    op.drop_index(op.f('ix_videos_tenant_id'), table_name='videos')
    op.drop_column('videos', 'tenant_id')
    op.drop_constraint(None, 'transcriptions', type_='foreignkey')
    op.drop_index(op.f('ix_transcriptions_tenant_id'), table_name='transcriptions')
    op.drop_column('transcriptions', 'tenant_id')
    op.drop_table('user_tenants')
    op.drop_index(op.f('ix_tenants_tenant_code'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_org_name'), table_name='tenants')
    op.drop_table('tenants')
    # ### end Alembic commands ###
