"""Add new colum to item table

Revision ID: bdb5a1fef90b
Revises: 3141cc32c3df
Create Date: 2025-06-24 12:58:55.984443

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'bdb5a1fef90b'
down_revision = '3141cc32c3df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('item', sa.Column('title_new', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('item', 'title_new')
    # ### end Alembic commands ###
