"""Add optional provider_user_id

Revision ID: 5c4e1aaeb2f0
Revises: fbe33cc01502
Create Date: 2025-06-25 11:02:57.852822

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '5c4e1aaeb2f0'
down_revision = 'fbe33cc01502'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('auth_credentials', 'provider_user_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('auth_credentials', 'provider_user_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###
