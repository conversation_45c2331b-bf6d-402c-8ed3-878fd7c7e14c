"""update status

Revision ID: 0ae44cedb050
Revises: cef97aae49bf
Create Date: 2025-07-09 16:15:14.588646

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0ae44cedb050'
down_revision = 'cef97aae49bf'
branch_labels = None
depends_on = None


def upgrade():
    # First, add the new enum value 'UPLOADED' to the existing enum
    op.execute("ALTER TYPE statusenum ADD VALUE IF NOT EXISTS 'UPLOADED'")
    op.execute("ALTER TYPE statusenum ADD VALUE IF NOT EXISTS 'UPLOADING'")


    # Note: No need to update existing records since 'FULLY_UPLOADED' doesn't exist in the database
    # The code was changed from using 'FULLY_UPLOADED' to 'UPLOADED' but the database never had 'FULLY_UPLOADED'


def downgrade():
    # Note: PostgreSQL doesn't support removing enum values directly
    # If you need to remove 'UPLOADED', you would need to recreate the enum type
    # For now, we'll leave the 'UPLOADED' value in the enum but not use it
    pass
