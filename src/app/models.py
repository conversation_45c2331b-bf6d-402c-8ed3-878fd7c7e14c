import uuid, logging
 
from pydantic import EmailStr, validator
import re
from enum import Enum
import os
from datetime import datetime, timedelta, timezone
from typing import Tuple
from typing import List, Optional,Union
from datetime import datetime
from sqlmodel import SQLModel, Field, Relationship,create_engine, Session
from sqlalchemy import Column, DateTime,desc, Enum as SqlEnum,Text
from sqlalchemy.sql import func
from app.core.config import settings
from fastapi import status as http_status
from uuid import UUID
from pydantic import BaseModel
 
from sqlalchemy.orm import sessionmaker, relationship, declarative_base
from sqlalchemy.orm.exc import NoResultFound, MultipleResultsFound
from sqlalchemy.sql import func
from config import setup_logging
from app.utils import extract_video_id, is_youtube_link
from app.exceptions import GenericException
from app.core.config import (
    # setup_logging,
    TEMP_DIR,
    TEMP_DIR1
)

from pydantic import ValidationError
from app.exception_log import log_status

logger = setup_logging("Models")
# Shared properties
class UserBase(SQLModel):
    name: str = Field(..., max_length=255)
    mobile: str = Field(..., max_length=15)
    email: EmailStr = Field(..., max_length=255)
    password: str = Field(..., min_length=8, max_length=40)
    nationality: str = Field(..., max_length=100)
    is_active: bool = True
    is_superuser: bool = False
    

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


# class Role(SQLModel, table=True):
#     id: Optional[int] = Field(default=None, primary_key=True)
#     name: str = Field(index=True, unique=True)
#     users: List["User"] = Relationship(back_populates="role")

class UserRegister(SQLModel):
    name: str = Field(..., max_length=255)
    mobile: str = Field(..., max_length=15)
    email: EmailStr = Field(..., max_length=255)
    password: str = Field(..., min_length=8, max_length=40)
    nationality: str = Field(..., max_length=100)
    verification_code: str = Field(..., max_length=6)
    tenant_code: str = Field(default="ASL001")  
    # role_name: str = Field(default="Guest User")
    @validator("mobile")
    def validate_mobile(cls, v):
        if v is None:
            return v
        pattern = re.compile(r'^(?:\+91[-\s]?)?[6-9]\d{9}$')  # Accepts "+91-9876543210" or "9876543210"
        if not pattern.fullmatch(v):
            raise ValueError("Invalid mobile number format")
        return v
    


# Properties to receive via API on update, all are optional
class UserUpdate(BaseModel):
    email: EmailStr = Field(..., max_length=255)
    name: str = Field(..., max_length=255)
    mobile: str = Field(..., max_length=15)
    nationality: str = Field(..., max_length=100)

class SsoUserUpdate(BaseModel):
    email: EmailStr = Field(..., max_length=255)
    name: str = Field(..., max_length=255)
    mobile: str = Field(..., max_length=15)
    nationality: str = Field(..., max_length=100)
    tenant_code: str = Field(default="ASL001")


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# # Database model, database table inferred from class name
# class User(UserBase, table=True):
#     id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
#     hashed_password: str
#     # items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
#     role_id: Optional[int] = Field(default=None, foreign_key="role.id")  # no trailing comma
#     role: Optional[Role] = Relationship(back_populates="users")


# Properties to return via API, id is always required
class UserPublic(SQLModel):
    email: EmailStr = Field(..., max_length=255)
    name: str = Field(..., max_length=255)
    mobile: Optional[str] = Field(default=None, max_length=15)
    nationality: Optional[str] = Field(default=None, max_length=100)


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


 
class RefreshTokenRequest(BaseModel):
    refresh_token: str

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class VerifyCodeRequest(BaseModel):
    email: EmailStr
    code: str

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    code: str
    new_password: str

class EmailVerificationRequest(BaseModel):
    email: EmailStr


# class for email verification 
class EmailVerification(SQLModel, table=True):
    __tablename__ = "email_verification"
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    email: str = Field(index=True, max_length=100)
    code: str
    expires_at: datetime
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime, nullable=False, server_default=func.now()),
    )

class Tenant(SQLModel, table=True):
    """Multi-tenant organization model """
    __tablename__ = "tenants"

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    org_name: str = Field(max_length=255, index=True)
    tenant_code: str = Field(max_length=50, unique=True, index=True, nullable=False)
    is_active: bool = Field(default=True)
    email: Optional[str] = Field(default=None, max_length=255)
    phone: Optional[str] = Field(default=None, max_length=20)
    address: Optional[str] = Field(default=None, max_length=500)
    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )

    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")

    # Relationships
    users: List["UserTenant"] = Relationship(back_populates="tenant")

class User(SQLModel, table=True):
    __tablename__ = "users"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    name: str = Field(index=True, max_length=100)
    email: str = Field(index=True,  unique=True, max_length=100)
    mobile: Optional[str] = Field(default=None, nullable=True)       
    nationality: Optional[str] = Field(default=None, nullable=True)
    is_active: bool = True
    is_superuser: bool = False

    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )

    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")

    tenants: List["UserTenant"] = Relationship(back_populates="user")
    credentials: List["AuthCredential"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "[AuthCredential.user_id]"}
    )


class AuthCredential(SQLModel, table=True):
    __tablename__ = "auth_credentials"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="users.id")  # match new table name
    provider: str
    provider_user_id: Optional[str] = Field(default=None, nullable=True)
    password_hash: Optional[str] = None

    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )

    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")

    user: Optional[User] = Relationship(
        back_populates="credentials",
        sa_relationship_kwargs={"foreign_keys": "[AuthCredential.user_id]"}
    )

class UserTenant(SQLModel, table=True):
    """User-Tenant relationship """
    __tablename__ = "user_tenants"

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="users.id", nullable=False)
    tenant_id: UUID = Field(foreign_key="tenants.id", nullable=False)
    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )

    created_by: Optional[UUID] = Field(default=None)
    updated_by: Optional[UUID] = Field(default=None)
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None)
    
    # Relationships
    user: Optional["User"] = Relationship(back_populates="tenants")
    tenant: Optional["Tenant"] = Relationship(back_populates="users")

class PasswordReset(SQLModel, table=True):
    __tablename__ = "password_reset"
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    email: str = Field(index=True)
    code: str
    expires_at: datetime
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime, nullable=False, server_default=func.now()),
    )




class CaptionData(SQLModel):
    id: int
    video_id: int
    start_time: float
    end_time: float
    word: List[str]
    root: List[str]
    status: str

class AudioCaptionData(SQLModel):
    id: int
    audio_id: int
    start_time: float
    end_time: float
    word: List[str]
    root: List[str]
    status: str


class CheckStatusResponse(SQLModel):
    status: bool
    session_id: Union[int, str]
    data: List[CaptionData]

class CheckStatusInput(SQLModel):
    video_link: str

class GetCaptionInput(SQLModel):
    session_id: int 
    start_time: float

class GetCaptionOutput(SQLModel):
    data: List[CaptionData]
    status: Union[bool, str]

class LargeAudioGetCaptionOutput(SQLModel):
    data: List[AudioCaptionData]
    status: Union[bool, str]

class CheckLargeAudioStatusResponse(SQLModel):
    status: bool
    session_id: Union[int, str]
    data: List[AudioCaptionData]


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title_new: str = Field(min_length=1, max_length=255)
    # owner_id: uuid.UUID = Field(
    #     foreign_key="user.id", nullable=False, ondelete="CASCADE"
    # )
    # owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    code: str
    new_password: str = Field(min_length=8, max_length=40)
    email: EmailStr




class WordRootPair(SQLModel):
    word: List[str]
    root: List[str]

class ConvertTextResponse(SQLModel):
    status: bool
    data: List[WordRootPair]

class ConvertTextRequest(SQLModel):
    arabic_text: str

class WordRootPairAudio(SQLModel):
    word: str
    root: str


class AudioTranscriptResponse(SQLModel):
    status: bool
    data: List[WordRootPairAudio]
    
# Define the Status enumeration (used by both transcriptions and audio uploads)
class StatusEnum(str, Enum):
    IN_PROGRESS = "IN PROGRESS"
    COMPLETED = "COMPLETED"
    ERROR = "ERROR"
    QUEUED = "QUEUED"
    UPLOADED = "UPLOADED"
    UPLOADING = "UPLOADING"
 
class Videos(SQLModel, table=True):
    # _tablename__ = "Videos"  if u want table name to be specific
    id : int = Field(default=None, primary_key=True,index=True)
    youtube_id : str = Field(nullable=False)
    url : str = Field(nullable=False)
    status : StatusEnum = Field(sa_column=Column(SqlEnum(StatusEnum), nullable=False))
    tenant_id: UUID = Field(foreign_key="tenants.id", nullable=False,index=True)
    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        sa_column=Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    )
    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id",index=True)
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    transcriptions : List["Transcriptions"] = Relationship(
        # "Transcriptions",
        back_populates="video",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"}
        )
 
class Transcriptions(SQLModel, table=True):
    id : int = Field(primary_key=True, index=True)
    video_id : int = Field(foreign_key="videos.id", index=True, nullable=False)
    start_time : float = Field(nullable=False)
    end_time : float = Field(nullable=False)
    word : str = Field(nullable=False)
    root : str = Field(nullable=False)
    chunk :int = Field(nullable=False)
    status : StatusEnum = Field(sa_column=Column(SqlEnum(StatusEnum), nullable=False))
    tenant_id: UUID = Field(foreign_key="tenants.id", nullable=False,index=True)
    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )
    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id",index=True)
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    video : Optional[Videos] = Relationship(
        # "Videos", 
        back_populates="transcriptions"
        )


 
class Audios(SQLModel, table=True):
    # _tablename__ = "Audios"  if u want table name to be specific
    id : int = Field(default=None, primary_key=True,index=True)
    filename : str = Field(nullable=False)
    # hashed_content : str = Field(nullable=False)
    status : StatusEnum = Field(sa_column=Column(SqlEnum(StatusEnum), nullable=False))
    created_at: datetime = Field(
        
        sa_column=Column(DateTime, nullable=False, server_default=func.now()),
    )
    updated_at : datetime = Field(
        sa_column=Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    )
    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id",index=True)
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    audio_transcriptions : List["AudioTranscriptions"] = Relationship(
        # "Transcriptions",
        back_populates="audio",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"}
        )
    tenant_id: UUID = Field(foreign_key="tenants.id", nullable=False,index=True)
 
    
class AudioTranscriptions(SQLModel, table=True):
    id : int = Field(primary_key=True, index=True)
    audio_id : int = Field(foreign_key="audios.id", index=True, nullable=False)
    start_time : float = Field(nullable=False)
    end_time : float = Field(nullable=False)
    word : str = Field(nullable=False)
    root : str = Field(nullable=False)
    chunk :int = Field(nullable=False)
    status : StatusEnum = Field(sa_column=Column(SqlEnum(StatusEnum), nullable=False))
    created_at : datetime = Field(
        
        sa_column=Column(DateTime, server_default=func.now())
    )
    updated_at : datetime = Field(
        default=None,
        sa_column=Column(DateTime, onupdate=func.now())
    )
    created_by: Optional[UUID] = Field(default=None, foreign_key="users.id",index=True)
    updated_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    deleted_at: Optional[datetime] = None
    deleted_by: Optional[UUID] = Field(default=None, foreign_key="users.id")
    audio : Optional[Audios] = Relationship(
         back_populates="audio_transcriptions"
        )
    tenant_id: UUID = Field(foreign_key="tenants.id", nullable=False,index=True)
 
   
class ExceptionLog(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    exception_type: str  = Field(nullable=False)
    message: str = Field(sa_column=Column(Text, nullable=False))
    stack_trace: Optional[str] = Field(sa_column=Text())
    module: Optional[str]
    endpoint: Optional[str]
    file_name: Optional[str]
    line_number: Optional[int]
    status_code: Optional[int]
    user_id: Optional[UUID] = Field(default=None, foreign_key="users.id")
    created_at: datetime = Field(
    default_factory=lambda: datetime.now(timezone.utc),  
    sa_column=Column(DateTime(timezone=True), nullable=False, server_default=func.now()),
    )
    

class ExceptionLogCreate(SQLModel):
    exception_type: str
    message: str
    stack_trace: Optional[str]
    module: Optional[str]
    endpoint: Optional[str]
    file_name: Optional[str]
    line_number: Optional[int]
    status_code: Optional[int]
    user_id: Optional[UUID] = None
    
DATABASE_URL = settings.SQLALCHEMY_DATABASE_URI 

# Create the engine with advanced options
engine = create_engine(
    str(DATABASE_URL),
    connect_args={"connect_timeout": 10000},  # DB-specific
    pool_pre_ping=True,
    pool_recycle=300,
    echo=True,  # Optional: log SQL
)

# Create session factory like SQLAlchemy
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=Session  # ✅ Use SQLModel's Session
)

def video_exists(youtube_link: str) -> int:
    """Checks if video has been transcribed before.

    Args:
        youtube_link (str): Youtube video link

    Returns:
        int: Videos.id if video exists and status is completed else returns -1
    """
    try:
        log_status(logger,logging.INFO, f"Checking if video exists for youtube_link: {youtube_link}")
        if is_youtube_link(youtube_link):
            video_id = extract_video_id(youtube_link)
            if video_id is None:
                video_id = youtube_link
        else:
            video_id = youtube_link
        db = SessionLocal()
        result = (
            db.query(Videos)
            .filter(
                Videos.youtube_id == video_id,
                Videos.status.in_(["COMPLETED", "IN_PROGRESS", "QUEUED"]),
            )
            .one()
        )
        if result.status == "ERROR":
            return -1
        else:
            return result.id
    except MultipleResultsFound:
        log_status(logger,logging.INFO, f"Multiple completed ids found for youtube_id {video_id}")
        try:
            result = db.query(Videos).filter(
                Videos.youtube_id == video_id,
                Videos.status.in_(["COMPLETED"]),
            )
            return result[0].id
        except NoResultFound:
            try:
                result = db.query(Videos).filter(
                    Videos.youtube_id == video_id,
                    Videos.status.in_(["IN_PROGRESS"]),
                )
                return result[0].id
            except NoResultFound:
                result = db.query(Videos).filter(
                    Videos.youtube_id == video_id,
                    Videos.status.in_(["QUEUED"]),
                )
                return result[0].id
    except NoResultFound:
        log_status(logger,logging.INFO, f"Video not transcribed earlier for youtube_id {video_id}")
        return -1
    except Exception as e:
        log_status(logger,logging.INFO, f"Error while checking if video exists for youtube_link: {youtube_link}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e
        )
    finally:
        db.close()

def add_video_record(youtube_link: str, status: StatusEnum, user_id: UUID, tenant_id: UUID) -> int:
    """Adds a new row in videos table and returns the id of the row if successful or -1 if not

    Args:
        youtube_link (str): youtube video link
        status (StatusEnum): should be "IN_PROGRESS"

    Returns:
        int: Videos.id or -1
    """
    try:
        if is_youtube_link(youtube_link):
            video_id = extract_video_id(youtube_link)
            # print("video_id:",video_id)
            if video_id is None:
                video_id = youtube_link
        else:
            video_id = youtube_link
        db = SessionLocal()
        
        new_record = Videos(youtube_id=video_id, url=youtube_link, status=status, created_by=user_id, tenant_id= tenant_id)
        db.add(new_record)
        db.commit()
        db.refresh(new_record)
        db.close()
        return new_record.id
    except Exception as error:
        log_status(logger,logging.INFO, f"Error while adding video record for youtube_link: {youtube_link}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error
        )


def update_videos(session_id: int, status="COMPLETED") -> bool:
    """Updates the status of a Video and its most recent Transcription.

    This function updates the status of a Video entry and its most recent
    associated Transcription entry in the database to the specified status.

    Args:
        session_id (int): The id of the Video to update.
        status (str, optional): The status to set. Defaults to "COMPLETED".

    Returns:
        bool: Returns True if the update was successful, else False.

    Raises:
        Exception: Any exception raised during the database operation is caught,
                   logged, and results in a False return value.
    """
    try:
        log_status(logger,logging.INFO, f"Updating video status for session_id: {session_id}")
        db = SessionLocal()
        video = db.query(Videos).filter(Videos.id == session_id).one()

        # Query for the most recent transcription
        most_recent_transcription = (
            db.query(Transcriptions)
            .filter(Transcriptions.video_id == session_id)
            .order_by(Transcriptions.id.desc())
            .first()
        )

        video.status = status
        if most_recent_transcription:
            most_recent_transcription.status = status

        db.commit()
        db.refresh(video)
        db.close()

        if status == "COMPLETED" or status == "ERROR":
            try:
                os.remove(os.path.join(TEMP_DIR, f"{session_id}_video.mp4"))
                os.remove(os.path.join(TEMP_DIR, f"{session_id}_audio.wav"))
                os.remove(os.path.join(TEMP_DIR, f"{session_id}.wav"))
            except FileNotFoundError:
                pass
        return True
    except Exception as error:
        log_status(logger,logging.INFO, f"Error while updating video status for session_id: {session_id}")
        return False


# Example of using the session to add a new record
def add_session_record(
    video_id: int,
    start_time: float,
    end_time: float,
    word: str,
    root: str,
    chunk: int,
    status: StatusEnum,
    user_id: UUID,
    tenant_id: UUID
) -> bool:
    """
Adds a new transcription session record to the database.

Args:
    video_id (int): The ID of the video associated with the transcription.
    start_time (float): The start time of the transcription segment in seconds.
    end_time (float): The end time of the transcription segment in seconds.
    word (str): The transcribed word.
    root (str): The root form of the transcribed word.
    chunk (int): The chunk number or identifier for the transcription segment.
    status (StatusEnum): The status of the transcription (e.g., pending, completed).

Returns:
    bool: True if the record was successfully added, False otherwise.
"""
    try:
        # print("Running add_session_record")
        log_status(logger,logging.INFO, f"Adding transcription record for video_id {video_id} from {start_time} to {end_time}")
        db = SessionLocal()
        new_record = Transcriptions(
            video_id=video_id,
            start_time=start_time,
            end_time=end_time,
            word=word,
            root=root,
            chunk=chunk,
            status=status,
            created_by=user_id,
            tenant_id=tenant_id
        )
        db.add(new_record)
        db.commit()
        db.refresh(new_record)
        db.close()
        return True
    except Exception as error:
        # logger.error("Failed to add data to database")
        log_status(logger,logging.INFO, f"Error while adding transcription record for video_id {video_id} from {start_time} to {end_time}")
        return False


async def filter_sessions(video_id: int, start_time_threshold: float) -> list:
    """
    Filters rows by session_id and start_time greater than the specified start_time_threshold.

    :param session_id: The session_id to filter by.
    :param start_time_threshold: The start_time threshold to filter by.
    :return: List of filtered session data as dictionaries.
    """
    db = SessionLocal()
    try:
        
        log_status(logger,logging.INFO, f"Filtering sessions for video_id {video_id} with start_time_threshold {start_time_threshold}")
        filtered_sessions = (
            db.query(Transcriptions)
            .filter(
                Transcriptions.video_id == video_id,
                Transcriptions.start_time >= start_time_threshold,
            )
            .order_by(Transcriptions.start_time)
            .all()
        )

        # Convert the SQLAlchemy objects to dictionaries
        filtered_sessions_dicts = [
            {
                "id": session.id,
                "video_id": session.video_id,
                "start_time": session.start_time,
                "end_time": session.end_time,
                "word": session.word.split(";"),
                "root": session.root.split(";"),
                "status": session.status.value,
            }
            for session in filtered_sessions
        ]

        return filtered_sessions_dicts
    except Exception as error:
        # logger.error("Failed to filter data from database")
        logger.exception("Failed to filter data from database")
        return []
    finally:
        db.close()


async def check_video_status(
    session_id: int,
) -> Tuple[bool, str]:
    """
    Check the transcription status of a video session.

    This function queries the database for the latest transcript of a given video session,
    checks its status, and returns appropriate status information. It handles various
    scenarios such as completed transcriptions, errors, and in-progress transcriptions.

    Parameters:
    -----------
    session_id : int
        The unique identifier of the video session to check.
    endpoint: str
        Used to identify which endpoint is calling this function.

    Returns:
    --------
    Tuple[bool, str]
        A tuple containing:
        - A boolean indicating the success status of the operation
        - A string message describing the current status or any errors

    Raises:
    -------
    Exception
        If there's an error during the database query or status check process.

    Notes:
    ------
    - The function updates the video status to "ERROR" if the latest transcript
      is older than 2 minutes.
    - It handles NoResultFound exception when no transcript is found for the given session.
    """
    try:
        db = SessionLocal()
        video = db.query(Videos).filter(Videos.id == session_id).one()
        log_status(logger,logging.INFO, f"Checking status for video {session_id} with status {video.status}")
        if video.status == StatusEnum.COMPLETED:
            return True, "Analysed the audio nothing more to transcribe"

        if video.status == StatusEnum.QUEUED:
            log_status(logger,logging.INFO, f"Video status is QUEUED for session {session_id}")
            return True, "Video status is QUEUED, Video is in Queue"

        transcripts = (
            db.query(Transcriptions)
            .filter(Transcriptions.video_id == session_id)
            .order_by(desc(Transcriptions.id))
            .all()
        )

        if len(transcripts) == 0:
            raise NoResultFound
        
        # Get the latest transcript
        latest_transcript = transcripts[0]
        current_time = datetime.now(latest_transcript.created_at.tzinfo)
        time_diff = current_time - latest_transcript.created_at

        if time_diff > timedelta(minutes=3):

            update_videos(session_id, "ERROR")
            return (
                False,
                "Error while transcribing audio: Time Delta > 2 mins for latest transcript",
            )

        else:

            if video.status == StatusEnum.COMPLETED:
                log_status(logger,logging.INFO, f"Video {session_id} status is COMPLETED")
                return True, "Analysed the audio nothing more to transcribe"

            elif video.status == StatusEnum.ERROR:
                log_status(logger,logging.INFO, f"Video {session_id} status is ERROR with no transcriptions")
                return False, "Error while transcribing audio: Video.status is error"

            elif video.status == StatusEnum.IN_PROGRESS:
                log_status(logger,logging.INFO, f"Video {session_id} status is IN_PROGRESS")
                return True, "Video is being transcribed"

    except NoResultFound:

        if video.status == StatusEnum.COMPLETED:
            log_status(logger,logging.INFO, f"Video {session_id} status is COMPLETED with no transcriptions")
            return True, "Analysed the audio nothing more to transcirbe"

        elif video.status == StatusEnum.ERROR:
            log_status(logger,logging.INFO, f"Video {session_id} status is ERROR with no transcriptions")
            return (
                False,
                "Error while transcribing audio: Video.status is error, No transcriptions found",
            )

        elif video.status == StatusEnum.IN_PROGRESS:
            log_status(logger,logging.INFO, f"Video {session_id} status is IN_PROGRESS with no transcriptions")
            current_time = datetime.now(video.created_at.tzinfo)
            time_diff = current_time - video.created_at

            if time_diff > timedelta(minutes=3):
                update_videos(session_id, "ERROR")
                return (
                    False,
                    "Error while transcribing audio: Video.status is in progress for more than 2 minutes, No transcriptions found",
                )
            else:
                return True, "Video is being transcribed"

    except Exception as e:
        log_status(logger,logging.ERROR, f"Error checking status for video {session_id}: {str(e)}")
        return False, "Error checking status for video"
    finally:
        db.close()
    

#function to insert into audio table
def add_audio_record(filename: str, status: StatusEnum, user_id: UUID) -> int:
    """Adds a new row in audios table and returns the id of the row if successful or -1 if not

    Args:
        filename (str): name of the audio file
        status (StatusEnum): should be "IN_PROGRESS"

    Returns:
        int: Audios.id or -1
    """
    log_status(logger,logging.INFO, f"Adding audio record for filename: {filename}")
    try:
        db = SessionLocal()
        
        new_record = Audios(
            filename=filename,  
            status=status,  
            created_by=user_id,  # Assuming user_id is provided
        )
        db.add(new_record)
        db.commit()
        db.refresh(new_record)
        db.close()
        return new_record.id
    except Exception as error:
        log_status(logger,logging.INFO, f"Error while adding audio record for filename: {filename}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error
        )

#function to insert into audio_transcription table
def add_audio_transcription_record(
    audio_id: int,
    start_time: float,
    end_time: float,
    word: str,
    root: str,
    chunk: int,
    status: StatusEnum,
    user_id: UUID,
    tenant_id: UUID
) -> bool:
    """
Adds a new transcription session record to the database.

Args:
    audio_id (int): The ID of the audio associated with the transcription.
    start_time (float): The start time of the transcription segment in seconds.
    end_time (float): The end time of the transcription segment in seconds.
    word (str): The transcribed word.
    root (str): The root form of the transcribed word.
    chunk (int): The chunk number or identifier for the transcription segment.
    status (StatusEnum): The status of the transcription (e.g., pending, completed).

Returns:
    bool: True if the record was successfully added, False otherwise.
"""
    try:
        log_status(logger,logging.INFO, f"Adding transcription record for audio_id {audio_id} from {start_time} to {end_time}")
        db = SessionLocal()
        new_record = AudioTranscriptions(
            audio_id=audio_id,
            start_time=start_time,
            end_time=end_time,
            word=word,
            root=root,
            chunk=chunk,
            status=status, 
            created_by=user_id,
            tenant_id=tenant_id       
        )
        db.add(new_record)
        db.commit()
        db.refresh(new_record)
        db.close()
        return True
    except Exception as error:
        # logger.error("Failed to add data to database")
        log_status(logger,logging.INFO, f"Error while adding transcription record for audio_id {audio_id} from {start_time} to {end_time}")
        return False

def update_audio_status(session_id: int, status="COMPLETED") -> bool:
    """Updates the status of a Audio and its most recent Transcription.

    This function updates the status of a Audio entry and its most recent
    associated Transcription entry in the database to the specified status.

    Args:
        session_id (int): The id of the Video to update.
        status (str, optional): The status to set. Defaults to "COMPLETED".

    Returns:
        bool: Returns True if the update was successful, else False.

    Raises:
        Exception: Any exception raised during the database operation is caught,
                   logged, and results in a False return value.
    """
    try:
        log_status(logger,logging.INFO, f"Updating audio status for session_id: {session_id}")
        db = SessionLocal()
        audio = db.query(Audios).filter(Audios.id == session_id).one()

        # Query for the most recent transcription
        most_recent_transcription = (
            db.query(AudioTranscriptions)
            .filter(AudioTranscriptions.audio_id == session_id)
            .order_by(AudioTranscriptions.id.desc())
            .first()
        )

        audio.status = status
        if most_recent_transcription:
            most_recent_transcription.status = status

        db.commit()
        db.refresh(audio)
        db.close()

        if status == "COMPLETED" or status == "ERROR":
            try:
                os.remove(os.path.join(TEMP_DIR1, f"{session_id}_audio.mp3"))
                os.remove(os.path.join(TEMP_DIR1, f"{session_id}_audio.wav"))
                os.remove(os.path.join(TEMP_DIR1, f"{session_id}.wav"))
            except FileNotFoundError:
                pass
        return True
    except Exception as error:
        log_status(logger,logging.INFO, f"Error while updating video status for session_id: {session_id}")
        return False

# to check if the video already exists
def is_audio_exists(filename: str) -> int:
    """Checks if audio has been transcribed before.

    Args:
        filename (str): filename of the audio file content
    Returns:
        int: Audios.id if audio exists and status is completed else returns -1
    """
    try:
        log_status(logger,logging.INFO, f"Checking if audio exists for audio file: {filename}")
        
        db = SessionLocal()
        result = (
            db.query(Audios)
            .filter(
                Audios.filename == filename,
                Audios.status.in_(["COMPLETED", "IN_PROGRESS", "QUEUED"]),
            )
            .one()
        )
        if result.status == "ERROR":
            return -1
        else:
            return result.id
    except MultipleResultsFound:
        log_status(logger,logging.INFO, f"Multiple completed ids found for audio file {filename}")
        try:
            result = db.query(Audios).filter(
                Audios.filename == filename,
                Audios.status.in_(["COMPLETED"]),
            )
            return result[0].id
        except NoResultFound:
            try:
                result = db.query(Audios).filter(
                    Videos.youtube_id == video_id,
                    Videos.status.in_(["IN_PROGRESS"]),
                )
                return result[0].id
            except NoResultFound:
                result = db.query(Audios).filter(
                    Audios.filename == filename,
                    Audios.status.in_(["QUEUED"]),
                )
                return result[0].id
    except NoResultFound:
        log_status(logger,logging.INFO, f"Audio not transcribed earlier for audio file {filename}")
        return -1
    except Exception as e:
        log_status(logger,logging.INFO, f"Error while checking if audio exists for audio file: {filename}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=e
        )
    finally:
        db.close()

async def filter_audio_sessions(audio_id: int, start_time_threshold: float) -> list:
    """
    Filters audio transcription rows by audio_id and start_time greater than the specified start_time_threshold.

    :param audio_id: The audio_id to filter by.
    :param start_time_threshold: The start_time threshold to filter by.
    :return: List of filtered audio transcription data as dictionaries.
    """
    db = SessionLocal()
    try:
        log_status(logger, logging.INFO, f"Filtering audio sessions for audio_id {audio_id} with start_time_threshold {start_time_threshold}")
        filtered_sessions = (
            db.query(AudioTranscriptions)
            .filter(
                AudioTranscriptions.audio_id == audio_id,
                AudioTranscriptions.start_time >= start_time_threshold,
            )
            .order_by(AudioTranscriptions.start_time)
            .all()
        )

        # Convert to list of dictionaries with grouped words and roots
        result = []
        current_group = None

        for session in filtered_sessions:
            # Group consecutive transcriptions with same timing
            if (current_group is None or
                abs(session.start_time - current_group["start_time"]) > 0.1 or
                abs(session.end_time - current_group["end_time"]) > 0.1):

                if current_group is not None:
                    result.append(current_group)

                current_group = {
                    "id": session.id,
                    "audio_id": session.audio_id,
                    "start_time": session.start_time,
                    "end_time": session.end_time,
                    "word": [session.word],
                    "root": [session.root],
                    "status": session.status.value,
                }
            else:
                # Add to current group
                current_group["word"].append(session.word)
                current_group["root"].append(session.root)

        # Add the last group
        if current_group is not None:
            result.append(current_group)

        log_status(logger, logging.INFO, f"Filtered {len(result)} audio session groups for audio_id {audio_id}")
        return result

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error filtering audio sessions for audio_id {audio_id}: {str(e)}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error filtering audio sessions: {str(e)}"
        )
    finally:
        db.close()
async def check_audio_status(audio_id: int) -> Tuple[bool, str]:
    """
    Check the transcription status of an audio session.

    This function queries the database for the latest transcript of a given audio session,
    checks its status, and returns appropriate status information. It handles various
    scenarios such as completed transcriptions, errors, and in-progress transcriptions.

    Parameters:
    -----------
    audio_id : int
        The unique identifier of the audio session to check.

    Returns:
    --------
    Tuple[bool, str]
        A tuple containing:
        - A boolean indicating the success status of the operation
        - A string message describing the current status or any errors

    Raises:
    -------
    Exception
        If there's an error accessing the database or processing the audio status.
    """
    db = SessionLocal()
    try:
        log_status(logger, logging.INFO, f"Checking audio status for audio_id {audio_id}")

        # Get the audio record
        audio = db.query(Audios).filter(Audios.id == audio_id).first()
        if not audio:
            return False, f"Audio with id {audio_id} not found"

        # Check if audio processing is completed
        if audio.status == StatusEnum.COMPLETED:
            return True, "Audio transcription completed successfully"

        # Check if there's an error
        if audio.status == StatusEnum.ERROR:
            return False, "Error occurred during audio transcription"

        # Get transcription records to check progress
        transcripts = (
            db.query(AudioTranscriptions)
            .filter(AudioTranscriptions.audio_id == audio_id)
            .order_by(desc(AudioTranscriptions.id))
            .all()
        )

        if len(transcripts) == 0:
            # No transcriptions yet, check if processing has started
            if audio.status == StatusEnum.IN_PROGRESS:
                log_status(logger, logging.INFO, f"Audio {audio_id} status is IN_PROGRESS with no transcriptions")
                current_time = datetime.now(audio.updated_at.tzinfo) if audio.updated_at and audio.updated_at.tzinfo else datetime.now()
                time_diff = current_time - (audio.updated_at or audio.created_at)

                if time_diff > timedelta(minutes=5):
                    update_audio_status(audio_id, "ERROR")
                    return False, "Error: Audio processing timed out"
                else:
                    return True, "Audio is being processed"
            elif audio.status == StatusEnum.QUEUED:
                return True, "Audio is queued for processing"
            elif audio.status == StatusEnum.UPLOADED:
                return True, "Audio uploaded, waiting to start processing"
            else:
                return False, f"Audio status: {audio.status.value}"

        # Get the latest transcript
        latest_transcript = transcripts[0]
        current_time = datetime.now(latest_transcript.created_at.tzinfo) if latest_transcript.created_at.tzinfo else datetime.now()
        time_diff = current_time - latest_transcript.created_at

        if time_diff > timedelta(minutes=5):
            update_audio_status(audio_id, "ERROR")
            return False, "Error: Audio transcription timed out"

        # Check overall status
        if audio.status == StatusEnum.IN_PROGRESS:
            return True, "Audio transcription in progress"
        elif audio.status == StatusEnum.COMPLETED:
            return True, "Audio transcription completed"
        else:
            return True, f"Audio status: {audio.status.value}"

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error checking status for audio {audio_id}: {str(e)}")
        return False, "Error checking audio status"
    finally:
        db.close()

# Audio transcription models that mirror video transcription patterns
class AudioCaptionData(SQLModel):
    id: int
    audio_id: int
    start_time: float
    end_time: float
    word: List[str]
    root: List[str]
    status: str


class AudioCheckStatusResponse(SQLModel):
    status: bool
    session_id: Union[int, str]
    data: List[AudioCaptionData]


class AudioCheckStatusInput(SQLModel):
    session_id: int  # Using session_id for consistency with video transcription pattern


class AudioGetCaptionInput(SQLModel):
    session_id: int
    start_time: float


class AudioGetCaptionOutput(SQLModel):
    data: List[AudioCaptionData]
    status: Union[bool, str]


class AudioStartTranscriptionInput(SQLModel):
    filename: str


class AudioStartTranscriptionResponse(SQLModel):
    status: bool
    session_id: int
    message: str

class CreateSessionRequest(SQLModel):
    """
    Request model for creating a new upload session.
    """
    filename: str

class FinalizeUploadRequest(SQLModel):
    """
    Request model for finalizing a chunked upload session.
    """
    session_id: str

class SessionStatusRequest(SQLModel):
    """
    Request model for checking the status of a chunked upload session.
    """
    session_id: str

class ChunkInfo(SQLModel):
    """
    Model for basic information about an uploaded chunk.
    """
    chunk_number: int
    filename: str

class SessionStatusResponse(SQLModel):
    """
    Response model for the session status endpoint.
    """
    status: bool
    session_id: str
    chunks_received: Optional[int] = None
    processing_complete: Optional[bool] = None
    last_access: Optional[str] = None
    chunks_info: Optional[List[ChunkInfo]] = None
    message: str