from fastapi import status, Request
from fastapi.responses import JSONResponse


class GenericException(Exception):
    def __init__(self, status_code: int, detail: str):
        self.status_code = status_code
        self.detail = str(detail)


class BadRequestException(GenericException):
    def __init__(self, detail: str):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)


async def generic_exception_handler(request: Request, exc: GenericException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": False, "detail": exc.detail},
    )


async def bad_request_exception_handler(request: Request, exc: BadRequestException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": False, "detail": exc.detail},
    )
