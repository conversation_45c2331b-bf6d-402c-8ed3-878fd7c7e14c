from fastapi import APIRouter

from app.api.routes import items, login, private, users, utils,convert_text_router,upload_voice_router,video_transcription_router
from app.core.config import settings
from starlette.middleware.sessions import SessionMiddleware
from config import setup_logging
from app.core.config import (
    setup_app,
    # PORT,
    # setup_logging,
)
api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)
api_router.include_router(convert_text_router.router)
api_router.include_router(upload_voice_router.router)
api_router.include_router(video_transcription_router.router)
if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)

logger = setup_logging("fastapi")
print("Starting the application")



# Setup
setup_app()