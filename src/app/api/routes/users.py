import uuid, logging
from uuid import UUID
from typing import Any
from fastapi import status as http_status
from fastapi import Request, status
from sqlalchemy.exc import SQLAlchemyError
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlmodel import col, delete, func, select

from app.exceptions import BadRequestException, GenericException
from app import crud
from app.api.deps import (
    CurrentUser,
    SessionDep,
    get_current_active_superuser,
    get_current_active_tenant
)
from app.core.config import settings
from app.core.security import get_password_hash, verify_password
from app.exception_log import log_exception_to_db, log_status
from config import setup_logging
from app.models import (
    Item,
    Message,
    UpdatePassword,
    User,
    UserCreate,
    UserPublic,
    UserRegister,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
    EmailVerification,
    Tenant,
    UserTenant,
    SsoUserUpdate,
    AuthCredential
    
)
from app.utils import generate_new_account_email, send_email
from datetime import datetime, timezone

router = APIRouter(prefix="/users", tags=["users"])
logger = setup_logging("users")

 
# @router.get(
#     "/",
#     dependencies=[Depends(get_current_active_superuser)],
#     response_model=UsersPublic,
# )
def read_users(session: SessionDep, skip: int = 0, limit: int = 100) -> Any:
    """
    Retrieve users.
    """

    count_statement = select(func.count()).select_from(User)
    count = session.exec(count_statement).one()

    statement = select(User).offset(skip).limit(limit)
    users = session.exec(statement).all()

    return UsersPublic(data=users, count=count)


# @router.post(
#     "/", dependencies=[Depends(get_current_active_superuser)], response_model=UserPublic
# )
def create_user(*, session: SessionDep, user_in: UserCreate) -> Any:
    """
    Create new user.
    """
    user = crud.get_user_by_email(session=session, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this email already exists in the system.",
        )

    user = crud.create_user(session=session, user_create=user_in)
    if settings.emails_enabled and user_in.email:
        email_data = generate_new_account_email(
            email_to=user_in.email, username=user_in.email, password=user_in.password
        )
        send_email(
            email_to=user_in.email,
            subject=email_data.subject,
            html_content=email_data.html_content,
        )
    return user


# @router.patch("/me", response_model=UserPublic)
def update_user_me(
    *, session: SessionDep, user_in: UserUpdateMe, current_user: CurrentUser
) -> Any:
    """
    Update own user.
    """

    if user_in.email:
        existing_user = crud.get_user_by_email(session=session, email=user_in.email)
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(
                status_code=409, detail="User with this email already exists"
            )
    user_data = user_in.model_dump(exclude_unset=True)
    current_user.sqlmodel_update(user_data)
    session.add(current_user)
    session.commit()
    session.refresh(current_user)
    return current_user


# @router.patch("/me/password", response_model=Message)
# def update_password_me(
#     *, session: SessionDep, body: UpdatePassword, current_user: CurrentUser
# ) -> Any:
#     """
#     Update own password.
#     """
#     if not verify_password(body.current_password, current_user.hashed_password):
#         raise HTTPException(status_code=400, detail="Incorrect password")
#     if body.current_password == body.new_password:
#         raise HTTPException(
#             status_code=400, detail="New password cannot be the same as the current one"
#         )
#     hashed_password = get_password_hash(body.new_password)
#     current_user.hashed_password = hashed_password
#     session.add(current_user)
#     session.commit()
#     return Message(message="Password updated successfully")


@router.get("/me", response_model=UserPublic)
def read_user_me(
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    try:
        log_status(logger, logging.INFO, f"Fetching current user: {current_user.email}")
        return current_user
    except Exception as e:
        session.rollback()
        logger.error(f"Unexpected error in read_user_me for {getattr(current_user, 'email', 'unknown')}: {str(e)}")
        log_args = {
            "session": session,
            "exc": str(e),
            "endpoint": "read_user_me",
            "module": __name__,
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR
        }
        log_exception_to_db(**log_args)
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error occurred while fetching current user."
        )


# @router.delete("/me", response_model=Message)
def delete_user_me(session: SessionDep, current_user: CurrentUser) -> Any:
    """
    Delete own user.
    """
    if current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="Super users are not allowed to delete themselves"
        )
    session.delete(current_user)
    session.commit()
    return Message(message="User deleted successfully")


@router.post("/signup", response_model=UserPublic)
def register_user(session: SessionDep, user_in: UserRegister) -> Any:
    try:
        log_status(logger, logging.INFO, f"Signup attempt for email: {user_in.email}")
        # Check if the verification code exists and is valid
        now = datetime.now(timezone.utc)
        verification_record = session.query(EmailVerification).filter(
            EmailVerification.email == user_in.email,
            EmailVerification.code == user_in.verification_code,
            EmailVerification.expires_at > now
        ).first()

        if not verification_record:
            log_status(logger, logging.WARNING, f"Registration failed for {user_in.email}: Invalid or expired verification code.")
            return JSONResponse(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                content={
                    "status": False,
                    "message": "Registration failed",
                    "detail": "Invalid or expired verification code."
                }
            )
        #Create new user without the need to be logged in.
        user = crud.get_user_by_email(session=session, email=user_in.email)
        if user:
            log_status(logger, logging.WARNING, f"Registration failed: Email already exists ({user_in.email})")
            return JSONResponse(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                content={
                    "status": False,
                    "message": "Registration failed",
                    "detail": "A user with this email already exists."
                }
            )
        
        #Validate tenant
        tenant_code="ASL001" # Hardcoded default value
        tenant = session.exec(
            select(Tenant).where(Tenant.tenant_code == tenant_code)
        ).first()
        if not tenant:
            log_status(logger, logging.WARNING, f"Invalid tenant code for user: ({user_in.email})")
            return JSONResponse(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                content={"status": False, "message": "Invalid tenant code."}
            )

            
        user_create = UserCreate.model_validate(user_in)
        user = crud.create_user(session=session, user_create=user_create,tenant_id=tenant.id)
        log_status(logger, logging.INFO, f"Registration successful for email: {user_in.email}")
        return JSONResponse(
            status_code=http_status.HTTP_201_CREATED,
            content={"status": True,"message":"Registration successful. You can now log in."}
        )
    
    except GenericException as ge:
        logger.error(f"GenericException during registration for {user_in.email}: {ge}")
        raise ge
    except Exception as e:
        session.rollback()
        logger.error(f"Unexpected error during registration for {user_in.email}: {str(e)}")
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "endpoint": "register_user",
            "module": __name__,
            "status_code":http_status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        log_exception_to_db(**log_args)
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail='Unexpected error occurred during registration.'
        )
    


# @router.get("/{user_id}", response_model=UserPublic)
def read_user_by_id(
    user_id: uuid.UUID, session: SessionDep, current_user: CurrentUser
) -> Any:
    """
    Get a specific user by id.
    """
    user = session.get(User, user_id)
    if user == current_user:
        return user
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="The user doesn't have enough privileges",
        )
    return user




@router.patch("/me/details")
def update_user_by_email(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    current_tenant: UUID= Depends(get_current_active_tenant),
    user_in: UserUpdate,
) -> Any:
    try:
        log_status(logger, logging.INFO, f"Update attempt for user: {current_user.email}")
        if current_user.email != user_in.email:
            log_status(logger, logging.WARNING, f"User validation failed for update attempt: {current_user.email} != {user_in.email}")
            return JSONResponse(
                status_code=403,
                content={"status": False, "detail": "User validation failed"}
            )
        """
        Update a user's name, mobile number, or nationality based on email.
        """
        # Step 1: Get the user by email
        db_user = crud.get_user_by_email(session=session, email=user_in.email)
        if not db_user:
            log_status(logger, logging.WARNING, f"Update failed: User with email {user_in.email} does not exist")
            return JSONResponse(
                    status_code=404,
                    content={"status": False, "detail": "The user with this email does not exist in the system"}
                )

        # Step 2: Update only allowed fields
        if user_in.name is not None:
            db_user.name = user_in.name
        if user_in.mobile is not None:
            db_user.mobile = user_in.mobile
        if user_in.nationality is not None:
            db_user.nationality = user_in.nationality

        session.add(db_user)
        session.commit()
        session.refresh(db_user)
        log_status(logger, logging.INFO, f"User updated successfully: {db_user.email}")
        return JSONResponse(
            status_code=200,
            content={
                "status": True,
                "message": "User updated successfully",
                "user": {
                    "id": str(db_user.id),
                    "email": db_user.email,
                    "name": db_user.name,
                    "mobile": db_user.mobile,
                    "nationality": db_user.nationality,
                }
                
            }
        )

    except Exception as e:
        session.rollback()
        log_args = {
            "session": session,
            "exc": str(e),
            "endpoint": "update_user_by_email",
            "module": __name__,
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR
        }
        log_exception_to_db(**log_args)

        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user."
        )




@router.patch("/sso/me/details")
def update_sso_user_by_email(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    user_in: SsoUserUpdate,
) -> Any:
    try:
        log_status(logger, logging.INFO, f"Update attempt for user: {current_user.email}")
        if current_user.email != user_in.email:
            log_status(logger, logging.WARNING, f"User validation failed for update attempt: {current_user.email} != {user_in.email}")
            return JSONResponse(
                status_code=403,
                content={"status": False, "detail": "User validation failed"}
            )
        
        """
        Update a user's name, mobile number,nationality and tenant code based on email.
        """
        #Get the user by email
        db_user = crud.get_user_by_email(session=session, email=user_in.email)
        if not db_user:
            log_status(logger, logging.WARNING, f"SSo User update failed: User with email {user_in.email} does not exist")
            return JSONResponse(
                    status_code=404,
                    content={"status": False, "detail": "The user with this email does not exist in the system"}
                )
        
        user_cred = session.exec(
        select(AuthCredential).where(AuthCredential.user_id == db_user.id)
        ).first()

        if user_cred and user_cred.provider == "local":
             return JSONResponse(
                status_code=400,
                content={
                    "status": False,
                    "message": "SSO account cannot perform this operation"
                }
                )
        
        #Validate tenant
        tenant_code="ASL001" # Hardcoded default value
        tenant = session.exec(
            select(Tenant).where(Tenant.tenant_code == tenant_code)
        ).first()
        if not tenant:
            return JSONResponse(
                status_code=400,
                content={"status": False, "detail": "Invalid tenant code"}
            )

        # Step 2: Update only allowed fields
        if user_in.name is not None:
            db_user.name = user_in.name
        if user_in.mobile is not None:
            db_user.mobile = user_in.mobile
        if user_in.nationality is not None:
            db_user.nationality = user_in.nationality

        session.add(db_user)
        tenant_exists = session.exec(
        select(UserTenant).where(
            UserTenant.user_id == db_user.id,
            UserTenant.tenant_id == tenant.id
        )
        ).first()

        if not tenant_exists:
            session.add(UserTenant(user_id=db_user.id, tenant_id=tenant.id))

        
        

        # # Delete existing tenant mapping
        # session.exec(
        #     delete(UserTenant).where(UserTenant.user_id == db_user.id)
        # )

        # # Insert new tenant mapping
        # new_mapping = UserTenant(user_id=db_user.id, tenant_id=tenant.id)
        # session.add(new_mapping)

        # Finalize update
        session.commit()
        session.refresh(db_user)

        log_status(logger, logging.INFO, f"SSO User updated successfully: {db_user.email}")
        return JSONResponse(
            status_code=200,
            content={
                "status": True,
                "message": "SSO User updated successfully",
                # "user": {
                #     "id": str(db_user.id),
                #     "email": db_user.email,
                #     "name": db_user.name,
                #     "mobile": db_user.mobile,
                #     "nationality": db_user.nationality,
                # }
                
            }
        )

    except Exception as e:
        session.rollback()
        log_args = {
            "session": session,
            "exc": str(e),
            "endpoint": "update_sso_user_by_email",
            "module": __name__,
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR
        }
        log_exception_to_db(**log_args)

        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update sso user."
        )

# @router.delete("/{user_id}", dependencies=[Depends(get_current_active_superuser)])
def delete_user(
    session: SessionDep, current_user: CurrentUser, user_id: uuid.UUID
) -> Message:
    """
    Delete a user.
    """
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user == current_user:
        raise HTTPException(
            status_code=403, detail="Super users are not allowed to delete themselves"
        )
    statement = delete(Item).where(col(Item.owner_id) == user_id)
    session.exec(statement)  # type: ignore
    session.delete(user)
    session.commit()
    return Message(message="User deleted successfully")

@router.patch("/me/password")
def update_password(session: SessionDep, 
                    body: UpdatePassword, 
                    current_user: CurrentUser,
                    current_tenant: UUID= Depends(get_current_active_tenant)) -> Message:
    """
    Update password using Bearer token for authentication.
    """
    try:
        user = crud.get_auth_cred_from_userid(user_id=current_user.id, session=session)
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/users/me/password",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured while updating password. Please try again"
        )

    if not user:
        log_status(logger, logging.ERROR, f"User not found for User ID: {current_user.id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User account not found."
        )

    if not verify_password(plain_password=body.current_password, hashed_password=user.password_hash):
        log_status(logger, logging.ERROR, f"Old password provided is incorrect for user: {current_user.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Old password is incorrect."
        )

    try:
        user.password_hash = get_password_hash(password=body.new_password)
        session.add(user)
        session.commit()
    except SQLAlchemyError as db_err:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=db_err,
            endpoint="/users/me/password",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred while updating password."
        )
    log_status(logger, logging.INFO, f"Password updated successfully for user: {current_user.email}")
    return JSONResponse(
    status_code=status.HTTP_200_OK,
    content={
        "status": True,
        "message": "Password updated successfully."
    }
)