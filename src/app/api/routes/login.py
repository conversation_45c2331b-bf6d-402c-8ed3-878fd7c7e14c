import inspect, threading, logging
from datetime import timedelta
from typing import Annotated, Any
from app.exception_log import log_exception_to_db, log_status
from fastapi import APIRouter, Depends, HTTPException,Body
from fastapi.responses import HTMLResponse
from fastapi.security import OAuth2PasswordRequestForm
from authlib.integrations.base_client.errors import OAuthError 
from app import crud
from app.api.deps import CurrentUser, SessionDep,get_current_active_tenant
from app.core import security
from app.core.config import settings
from app.core.security import get_password_hash
from app.models import Message, NewPassword, Token, UserPublic,AuthCredential,User,RefreshTokenRequest, ForgotPasswordRequest, PasswordReset, VerifyCodeRequest, EmailVerificationRequest, EmailVerification,UserTenant,Tenant
from app.exceptions import BadRequestException,GenericException
from fastapi import status as http_status
from fastapi.responses import JSONResponse
import uuid
from uuid import UUID

from app.utils import (
    generate_password_reset_token,
    generate_reset_password_email,
    send_email,
    verify_password_reset_token,
)
from authlib.integrations.starlette_client import OAuth
from fastapi import Request, status
from starlette.responses import RedirectResponse
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from pydantic import BaseModel
from jose import JWTError
import time
from app.models import  UpdatePassword
from app.exceptions import BadRequestException
from datetime import datetime, timedelta, timezone
from config import setup_logging
import jwt 
from jwt import decode, ExpiredSignatureError, InvalidSignatureError, DecodeError
import random, string
from app.utils import render_email_template
from sqlalchemy.exc import SQLAlchemyError
from passlib.context import CryptContext
from sqlmodel import col, delete, func, select
import firebase_admin
from firebase_admin import credentials, auth
from firebase_admin import credentials, initialize_app

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
logger = setup_logging("login")
router = APIRouter(tags=["login"])
firbase_cred_path = settings.FIREBASE_CRED_PATH
cred = credentials.Certificate(firbase_cred_path)
initialize_app(cred)

@router.post("/login/access-token")
def login_access_token(
    session: SessionDep, 
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    try:
        user = crud.authenticate(
            session=session, email=form_data.username, password=form_data.password
        )
        
        if not user:
            log_status(logger, logging.WARNING, f"Login failed for email: {form_data.username} - Incorrect email or password.")
            return JSONResponse(
                    status_code=401,
                    content={"status": False, "detail": "Incorrect email or password"}
                )
        elif not user.is_active:
            log_status(logger, logging.WARNING, f"Login failed for email: {form_data.username} - Inactive user.")
            return JSONResponse(
                    status_code=403,
                    content={"status": False, "detail": "Inactive user"}
                )

        #Check for at least one active tenant
        tenant_exists = session.exec(
            select(UserTenant)
            .join(Tenant)
            .where(
                UserTenant.user_id == user.id,
                Tenant.is_active == True,
                Tenant.deleted_at.is_(None)  
            )
        ).first()

        if not tenant_exists:
            log_status(logger, logging.WARNING, f"Login failed: No active tenant for user {form_data.email}")
            return JSONResponse(
                status_code=403,
                content={"status": False, "detail": "No active tenant associated with this user"}
            )    
    
        # Generate access and refresh tokens
        access_token, refresh_token = security.create_tokens(session=session, user_id=user.id)
        log_status(logger, logging.INFO, f"Login successful for email: {form_data.username}")

        return JSONResponse(
            status_code=200,
            content={
                "status": True,
                "message": "Login successful",
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "role_id": 1  
            }
        )
        
        
    except GenericException as ge:
        raise ge
    
    except Exception as e:
        session.rollback()
        log_args = {
        "session": session,
        "exc": str(e),
        "endpoint": "login_access_token",
        "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
        "module": __name__,
        }
        log_exception_to_db(**log_args) 
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Login failed. Please try again later."
        )
    

@router.post("/token/refresh")
def refresh_token(session: SessionDep, 
                  body: RefreshTokenRequest,
                  ) -> JSONResponse:
    '''
    Refresh access token using a valid refresh token.

    Args:
        session (SessionDep): Database session dependency.
        body (RefreshTokenRequest): Request body containing the refresh token.

    Returns:
        JSONResponse: Contains the new access token and token type if successful.
    '''
    try:
        refresh_token = body.refresh_token
        JWT_ALGORITHM = settings.JWT_ALGORITHM
        payload = jwt.decode(refresh_token, settings.SECRET_KEY, algorithms=[JWT_ALGORITHM])
        if payload.get("type") != "refresh":
            log_status(logger, logging.WARNING, f"Token refresh failed: Invalid token type for user_id: {payload.get('sub')}")
            raise HTTPException(status_code=401, status = False,detail="Invalid token type")

        user_id = payload.get("sub")
        user = session.get(User, user_id)
        
        if not user or not user.is_active:
            log_status(logger, logging.WARNING, f"Token refresh failed: User not found or inactive: {user.email}")
            raise HTTPException(status_code=401, detail="User inactive or does not exist")
        
        access_token = security.create_jwt_token(subject=user_id, expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES))
        log_status(logger, logging.INFO, f"Token refresh successful for user_id: {user_id}")
        return JSONResponse(
            status_code=200,
            content={
                "status": True,
                "detail": "Access token refreshed successfully",
                "access_token": access_token,
                "token_type": "bearer",
                "role_id":1
            }
        )
    except ExpiredSignatureError:
        log_status(logger, logging.WARNING, "Token refresh failed: Refresh token expired")
        raise HTTPException(
            status_code=401,
            detail="Refresh token expired"
        )
    except InvalidSignatureError:
        log_status(logger, logging.WARNING, "Token refresh failed: Invalid refresh token signature")
        raise HTTPException(
            status_code=401,
            detail="Invalid refresh token signature"
        )
    except DecodeError:
        log_status(logger, logging.WARNING, "Token refresh failed: Invalid refresh token format")
        raise HTTPException(
            status_code=401,
            detail="Invalid refresh token format"
        )
    
    except Exception as e:
        session.rollback()
        # Log the exception to the database
        log_exception_to_db(
            session=session,
            exc=str(e),
            endpoint="refresh_token",
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            module=__name__
        )
        raise HTTPException(status_code=500, detail="Token refresh failed")

# @router.post("/login/test-token", response_model=UserPublic)
def test_token(current_user: CurrentUser) -> Any:
    """
    Test access token
    """
    return current_user


# @router.post("/password-recovery/{email}")
# def recover_password(email: str, session: SessionDep) -> Message:
#     """
#     Password Recovery
#     """
#     user = crud.get_user_by_email(session=session, email=email)

#     if not user:
#         raise HTTPException(
#             status_code=404,
#             detail="The user with this email does not exist in the system.",
#         )
#     auth_cred = crud.get_auth_cred_from_userid(user_id=user.id, session=session)
#     if not auth_cred:
#         raise HTTPException(
#             status_code=404,
#             detail="The user with this email does not exist in the system.",
#         )
#     if auth_cred.provider != AuthProvider.local:
#         raise HTTPException(
#             status_code=400,
#             detail="Password recovery is only available for signed-up accounts.",
#         )
#     password_reset_token = generate_password_reset_token(email=email)
#     email_data = generate_reset_password_email(
#         email_to=user.email, email=email, token=password_reset_token
#     )
#     send_email(
#         email_to=user.email,
#         subject=email_data.subject,
#         html_content=email_data.html_content,
#     )
#     return Message(message="Password recovery email sent")


@router.post("/password/reset")
def reset_password(session: SessionDep, body: NewPassword) -> Message:
    """
    Reset password
    """
    now = datetime.now(timezone.utc)

    # Validate reset code
    try:
        reset_record = session.query(PasswordReset).filter(
            PasswordReset.email == body.email,
            PasswordReset.code == body.code,
            PasswordReset.expires_at > now
        ).first()
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/reset",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured during password reset. Please try again."
        )

    if not reset_record:
        log_status(logger, logging.WARNING, f"Password reset attempt failed for email: {body.email} - Invalid or expired reset code.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset code."
        )

    # Get user by email
    try:
        user = crud.get_user_by_email(session=session, email=body.email)
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/reset",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured during password reset. Please try again"
        )

    if not user:
        log_status(logger, logging.WARNING, f"Password reset attempt failed for email: {body.email} - User not found.")
        raise HTTPException(
            status_code=404,
            detail="The user with this email does not exist.",
        )

    if not user.is_active:
        log_status(logger, logging.WARNING, f"Password reset attempt failed for email: {body.email} - User is inactive.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user is Inactive"
        )

    # Update the password
    try:
        hashed_password = get_password_hash(password=body.new_password)
        auth_cred = crud.get_auth_cred_from_userid(user_id=user.id, session=session)
        auth_cred.password_hash = hashed_password
        session.add(auth_cred)

        # Delete the reset record to invalidate the code
        session.delete(reset_record)

        session.commit()
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/reset",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password update failed."
        )
    log_status(logger, logging.INFO, f"Password reset successful for email: {body.email}")
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": True,
            "message": "Password updated successfully."
        }
    )


# @router.post(
#     "/password-recovery-html-content/{email}",
#     dependencies=[Depends(get_current_active_superuser)],
#     response_class=HTMLResponse,
# )
# def recover_password_html_content(email: str, session: SessionDep) -> Any:
#     """
#     HTML Content for Password Recovery
#     """
#     user = crud.get_user_by_email(session=session, email=email)

#     if not user:
#         raise HTTPException(
#             status_code=404,
#             detail="The user with this username does not exist in the system.",
#         )
#     password_reset_token = generate_password_reset_token(email=email)
#     email_data = generate_reset_password_email(
#         email_to=user.email, email=email, token=password_reset_token
#     )

#     return HTMLResponse(
#         content=email_data.html_content, headers={"subject:": email_data.subject}
#     )

def generate_reset_code(length=6):
    """Generate a random numeric code"""
    return ''.join(random.choices(string.digits, k=length))


@router.post("/password/forgot", status_code=status.HTTP_200_OK)
def request_password_reset(request: ForgotPasswordRequest, session: SessionDep):
    """Request a password reset code by email"""
    try:
        user = crud.get_user_by_email(session=session, email=request.email)
    except Exception as e:
        
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/forgot",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured during password reset. Please try again."
        )
    
    
    if not user:
        log_status(logger, logging.WARNING, f"Password reset attempt failed for email: {request.email} - User not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="The user does not exist."
        )
    
    

    # Generate a secure random code
    reset_code = generate_reset_code()

    try:
        # Remove any existing reset codes for this email
        session.query(PasswordReset).filter(PasswordReset.email == request.email).delete()

        # Create a new password reset entry
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=settings.PASSWORD_RESET_CODE_EXPIRE_MINUTES)
        reset_record = PasswordReset(
            email=request.email,
            code=reset_code,
            expires_at=expires_at
        )
        session.add(reset_record)
        session.commit()
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/forgot",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured during password reset. Please try again"
        )

    # Render email template
    try:
        html_content = render_email_template(
            template_name="reset_code.html",
            context={
                "reset_code": reset_code,
                "expires_minutes": 15,
                "app_name": settings.APP_NAME
            }
        )
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/forgot",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email template rendering failed."
        )

    # Send the reset code email
    try:
        send_email(
            email_to=request.email,
            subject="Your Password Reset Code",
            html_content=html_content
        )
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/password/forgot",
            module=__name__,
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send reset code email."
        )
    log_status(logger, logging.INFO, f"Password reset code sent to {request.email}")
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": True,
            "message": "The reset code has been sent to your registered email."
        }
    )

@router.post("/verify-code", status_code=status.HTTP_200_OK)
def verify_reset_code(request: VerifyCodeRequest, session: SessionDep):
    """Verify the reset code without changing the password"""
    now = datetime.now(timezone.utc)

    try:
        reset_record = session.query(PasswordReset).filter(
        PasswordReset.email == request.email,
        PasswordReset.code == request.code,
        PasswordReset.expires_at > now
    ).first()
    except Exception as e:
        log_args = {
            "session": session,
            "exc": e,
            "endpoint": "/verify-code",
            "module": __name__,
            }
        log_exception_to_db(**log_args)
        raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"Error occured during Password reset. Please try again"
    )
    if not reset_record:
        log_status(logger, logging.WARNING, f"Invalid or expired reset code for email: {request.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset code. Please request a new code."
        )
    log_status(logger, logging.INFO, f"Reset code verified for email: {request.email}")
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": True,
            "message": "Code verified successfully."
        }
    )

@router.post("/sso/login")
async def sso_login(request: Request,session: SessionDep):
    # Get the Authorization header
    auth_header = request.headers.get("authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        log_status(logger, logging.WARNING, "SSO login failed: No token provided")
        raise HTTPException(status_code=http_status.HTTP_401_UNAUTHORIZED, detail="No token provided")

    id_token = auth_header.split("Bearer ")[1]

    try:
        # Verify the Firebase ID token
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token["uid"]
        user_email = decoded_token.get("email")
        user_name = decoded_token.get("name")
        provider = decoded_token.get("firebase", {}).get("sign_in_provider")
        # Optional: Save or update user in your DB
        user_data = {
            "firebase_uid": user_id,
            "email": user_email,
            "name": user_name,
            "provider": provider
        }
        # log_status(logger, logging.INFO, f"SSO login successful for email: {user_email} (provider: {provider})")
        return crud.get_or_create_sso_user(
            session=session,
            email=user_email,
            name=user_name,
            provider=provider,
            provider_user_id= user_id
            
        )  

  
        # return JSONResponse(
        #     status_code=200,
        #     content={
        #         "status": True,
        #         "message": "Login successful",
        #         "access_token": result["access_token"],
        #         "refresh_token": result["refresh_token"],
        #         "token_type": "bearer",
        #         "role_id": 1
        #     }
        # )

        
    except GenericException as ge:
        raise ge
    

    
    except Exception as e:
        session.rollback()
        log_args = {
        "session": session,
        "exc": str(e),
        "endpoint":"sso_login",
        "status_code": http_status.HTTP_401_UNAUTHORIZED,
        "module": __name__,
        }
        log_exception_to_db(**log_args) 
        raise GenericException(
            status_code=http_status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
        )

@router.post("/email/verify")
def verify_email(session: SessionDep, body: EmailVerificationRequest):
    # check user already registered or not
    try:
        user = crud.get_user_by_email(session=session, email=body.email)
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="/email/verify",
            module=__name__,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during email verification. Please try again"
        )

    if user:
        log_status(logger, logging.WARNING, f"Email already exists: {body.email}")
        
        return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": False,
            "message": "The email already exists."
        }
    )


    # Generate a secure random code
    reset_code = generate_reset_code()

    try:
        # Remove any existing reset codes for this email
        session.query(EmailVerification).filter(EmailVerification.email == body.email).delete()

        # Create a new password reset entry
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=settings.VERIFY_EMAIL_CODE_EXPIRE_MINUTES)
        reset_record = EmailVerification(
            email=body.email,
            code=reset_code,
            expires_at=expires_at
        )
        session.add(reset_record)
        session.commit()
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=e,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="/email/verify",
            module=__name__,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occured during Email verification. Please try again"
        )
    
    # Render email template
    try:
        html_content = render_email_template(
            template_name="verify_email.html",
            context={
                "reset_code": reset_code,
                "expires_minutes": 15,
                "app_name": settings.APP_NAME,
            }
        )
    except Exception as e:
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/email/verify",
            module=__name__,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email. Please try again."
        )

    # Send the verification email
    try:
        send_email(
            email_to=body.email,
            subject="Your Email Verification Code",
            html_content=html_content
        )
    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=e,
            endpoint="/email/verify",
            module=__name__,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email. Please try again."
        )
    log_status(logger, logging.INFO, f"Email verification code sent to {body.email}")
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": True,
            "message": "The verification code has been sent to your email."
        }
    )

# API endpoint to verify the email verification code
@router.post("/email/code/verify", status_code=status.HTTP_200_OK)
def verify_email_code(request: VerifyCodeRequest, session: SessionDep):
    now = datetime.now(timezone.utc)

    try:
        reset_record = session.query(EmailVerification).filter(
        EmailVerification.email == request.email,
        EmailVerification.code == request.code,
        EmailVerification.expires_at > now
    ).first()
    except Exception as e:
        log_args = {
            "session": session,
            "exc": e,
            "endpoint": "/email/verify/code",
            "module": __name__,
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        log_exception_to_db(**log_args)
        raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"Unable to complete email verification. Please try again"
    )
    if not reset_record:
        log_status(logger, logging.WARNING, f"Invalid or expired verification code for {request.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification code. Please request a new code."
        )
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "status": True,
            "message": "Email verified successfully."
        }
    )