import logging
from fastapi import APIRouter, UploadFile, File, Form, status,Depends
from youtube_transcript_api import YouTubeTrans<PERSON><PERSON><PERSON>
from youtube_transcript_api.formatters import <PERSON><PERSON><PERSON><PERSON>atter
from pydantic import ValidationError
from fastapi.responses import JSONResponse
from fastapi import status as http_status
from fastapi import HTT<PERSON>Exception
from app.models import (
    video_exists, filter_sessions,
    check_video_status,GetCaptionOutput, GetCaptionInput,
    CheckStatusResponse
)
from app.exceptions import BadRequestException, GenericException
from app.utils import is_youtube_link, extract_video_id, parse_xml_to_json

from app.api.services.video_transcription_service import start_task, get_caption
from app.api.deps import SessionDep
from app.exception_log import log_exception_to_db, log_status
from app.api.deps import get_current_user, get_current_active_tenant
from config import setup_logging
from moviepy import VideoFileClip
from io import BytesIO
from uuid import UUID

router = APIRouter(tags=["start_transcription"])
logger = setup_logging("video_transcription")

@router.post("/transcription/start")
async def start_transcription(
    session: SessionDep,
    video_link: str = Form(...),
    file: UploadFile = File(...),
    current_user: str = Depends(get_current_user),
    current_tenant: UUID = Depends(get_current_active_tenant)
):
    """
    Handles the initiation of a video transcription task.
    This endpoint accepts a video link and an uploaded video file, then starts the transcription process asynchronously.
    It also handles exceptions, logging unexpected errors to the database.
    Args:
        session (SessionDep): Database session dependency.
        video_link (str): The URL link to the video to be transcribed, provided as a form field.
        file (UploadFile): The uploaded video file to be transcribed.
        current_user (str): The currently authenticated user, injected via dependency.
    Returns:
        JSONResponse: The response from the transcription task initiation.
    Raises:
        GenericException: For known, handled exceptions.
        Exception: For any other exceptions, which are logged to the database before being re-raised.
    """
    try: 
                
        # Validate file format
        if file and not file.filename.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.wav','webm')):
            raise BadRequestException("Unsupported file type.")
        
        # Validate YouTube link
        if not is_youtube_link(video_link):
            raise BadRequestException("Invalid YouTube link.")

        # Validate file size (e.g., max 100MB)
        MAX_FILE_SIZE_MB = 100
        contents = await file.read()
        if len(contents) > MAX_FILE_SIZE_MB * 1024 * 1024:
            raise BadRequestException("File too large.")
        # reset file pointer
        file.file.seek(0)
        # Get user id
        user_id = current_user.id

        user_id = current_user.id
        return await start_task(video_link, file, user_id, current_tenant)
    except GenericException:
        raise
    except Exception as e:
        log_status(logger, logging.ERROR, f"Unexpected error in start_transcription: {str(e)}")
        # Log the exception to the database
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "start_transcription",
            "module": __name__,
            }
        log_exception_to_db(**log_args)
        raise 


@router.get("/transcription/status", response_model=CheckStatusResponse)
async def check_status(session: SessionDep, video_link: str,
                       current_user: str = Depends(get_current_user),
                       current_tenant: UUID = Depends(get_current_active_tenant)
                       ):
    """
    Asynchronously checks the transcription status of a video based on its link.

    Args:
        session (SessionDep): Database session dependency.
        video_link (str): The link to the video whose status is to be checked.
        current_user (str, optional): The currently authenticated user, injected by dependency.

    Returns:
        dict: A dictionary containing:
            - "status" (bool): True if the video session exists and is processed, False otherwise.
            - "session_id" (str): The session ID if found, empty string otherwise.
            - "data" (list): Filtered session data if available, empty list otherwise.

    Raises:
        Exception: Any exception encountered during processing is logged to the database and re-raised.
    """
    try: 
        session_id = video_exists(video_link)
        if session_id == -1:
            return {"status": False, "session_id": "", "data": []}
        else:
            status, message = await check_video_status(session_id)
            if status:
                data = await filter_sessions(session_id, 0)
                return {"status": True, "session_id": session_id, "data": data}
            else:
                return {"status": False, "session_id": "", "data": []}
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error checking video status for {video_link}: {str(e)}")
        # Log the exception to the database
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "check_video_status",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise 


@router.post("/transcription/captions",response_model=GetCaptionOutput)
async def get_caption_route(session: SessionDep, data: GetCaptionInput,
                            current_user: str = Depends(get_current_user),
                            current_tenant: UUID = Depends(get_current_active_tenant)
                            ):
    """
    Handles the retrieval of video captions for a given input.

    Args:
        session (SessionDep): Database session dependency.
        data (GetCaptionInput): Input data required to fetch captions.
        current_user (str, optional): The currently authenticated user, injected via dependency.

    Returns:
        The result of the `get_caption` function, containing the requested captions.

    Raises:
        Exception: If an error occurs during caption retrieval, logs the exception and re-raises it.
    """
    try:
        return await get_caption(data,session)
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error retrieving captions: {str(e)}")
        # Log the exception to the database
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "/transcription/captions",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise 
