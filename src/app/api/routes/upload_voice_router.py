# routes.py
import logging
import time
from typing import Set
from fastapi import APIRouter, UploadFile, File, Depends
from app.models import AudioTranscriptResponse

from app.api.services.upload_voice_service import generate_file_transcript
from app.api.deps import SessionDep
from app.exception_log import log_exception_to_db, log_status
from fastapi import status
from config import setup_logging
from app.exceptions import BadRequestException, GenericException
from app.api.deps import get_current_user, get_current_active_tenant
from uuid import UUID
from pathlib import Path
from app.models import (
    AudioTranscriptResponse,  CreateSessionRequest, Audios, StatusEnum,
    AudioGetCaptionOutput, AudioGetCaptionInput, AudioStartTranscriptionResponse
)

from app.api.services.upload_voice_service import generate_file_transcript, process_chunked_audio, chunk_manager
from app.api.services.audio_transcription_service import start_audio_task_for_session, get_audio_caption
from app.api.deps import SessionDep
from app.exception_log import log_exception_to_db, log_status
from fastapi import status,Form
import os
from config import setup_logging, TEMP_DIRE
from app.exceptions import BadR<PERSON><PERSON><PERSON>x<PERSON>, GenericException
from app.api.deps import get_current_user, get_current_active_superuser



router = APIRouter(tags=["upload_voice"])
logger = setup_logging("upload_voice")

# Constants for optimization
MAX_FILE_SIZE_MB = 25
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
SUPPORTED_EXTENSIONS: Set[str] = {'.wav', '.mp3', '.ogg', '.m4a', '.aac', '.amr'}
AUDIO_TIMEOUT_SECONDS = 60


def validate_audio_file(file: UploadFile) -> None:
    """
    Validate audio file for security and performance.

    Args:
        file: Uploaded file to validate

    Raises:
        BadRequestException: If validation fails
    """
    if not file or not file.filename:
        raise BadRequestException(detail="No file uploaded.")

    # Sanitize filename to prevent path traversal attacks
    filename = file.filename.strip()
    if not filename or '..' in filename or '/' in filename or '\\' in filename:
        raise BadRequestException(detail="Invalid filename.")

    # Check file extension efficiently
    file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
    if f'.{file_ext}' not in SUPPORTED_EXTENSIONS:
        raise BadRequestException(detail="Unsupported file type.")


def validate_file_contents(contents: bytes) -> None:
    """
    Validate file contents for size and emptiness.

    Args:
        contents: File contents as bytes

    Raises:
        BadRequestException: If validation fails
    """
    if not contents or len(contents) == 0:
        raise BadRequestException(detail="Uploaded file is empty.")

    if len(contents) > MAX_FILE_SIZE_BYTES:
        raise BadRequestException(detail=f"File too large. Maximum size is {MAX_FILE_SIZE_MB}MB.")


def log_performance_metrics(processing_time: float, file_size: int, filename: str, user_id: str):
    """Log performance metrics for monitoring"""
    mb_per_second = (file_size / (1024 * 1024)) / processing_time if processing_time > 0 else 0

    log_status(logger, logging.INFO,
              f"Performance metrics - User: {user_id}, File: {filename}, "
              f"Time: {processing_time:.3f}s, Size: {file_size/1024/1024:.2f}MB, "
              f"Speed: {mb_per_second:.2f} MB/s")


@router.post("/voice/upload", response_model=AudioTranscriptResponse)
async def upload_voice(
    session: SessionDep,
    file: UploadFile = File(...),
    current_user: UUID = Depends(get_current_user),
    current_tenant: UUID = Depends(get_current_active_tenant)
):
    """
    Optimized audio file transcription endpoint with enhanced validation and performance monitoring.

    Args:
        session (SessionDep): Database session dependency.
        file (UploadFile): The audio file to be processed.
        current_user (str): The current user making the request.

    Returns:
        AudioTranscriptResponse: A response model containing the transcript of the audio file.

    Raises:
        BadRequestException: If validation fails.
        GenericException: If an error occurs during processing.
    """
    start_time = time.time()
    filename = None

    try:
        log_status(logger, logging.INFO, f"User {current_user} uploading audio file")

        # Fast validation - check file first before reading contents
        validate_audio_file(file)
        filename = file.filename

        # Read file contents efficiently
        log_status(logger, logging.INFO, f"Reading file contents: {filename}")
        contents = await file.read()

        # Validate file contents
        validate_file_contents(contents)

        # Log processing info
        file_size = len(contents)
        log_status(logger, logging.INFO,
                  f"Processing audio file: {filename}, Size: {file_size/1024/1024:.2f}MB")

        # Process the audio file
        log_status(logger, logging.INFO, f"Generating transcript for file: {filename}")
        result = await generate_file_transcript(filename, contents)

        # Log performance metrics
        processing_time = time.time() - start_time
        log_performance_metrics(processing_time, file_size, filename, current_user)

        return result

    except BadRequestException:
        # Re-raise validation errors as-is
        processing_time = time.time() - start_time
        log_status(logger, logging.WARNING,
                  f"Validation error after {processing_time:.3f}s for file: {filename}")
        raise

    except MemoryError:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Memory error after {processing_time:.3f}s - file too large: {filename}")
        raise GenericException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="Audio file too large to process"
        )

    except TimeoutError:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Timeout error after {processing_time:.3f}s for file: {filename}")
        raise GenericException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Audio processing timed out"
        )

    except Exception as e:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Error processing file {filename} after {processing_time:.3f}s: {type(e).__name__}: {e}")

        # Log the exception to the database
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "upload_voice",
            "module": __name__,
        }
        log_exception_to_db(**log_args)

        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Audio transcription service temporarily unavailable"
        )




@router.post("/voice/upload/status")
async def check_upload_status(
    session_id: int = Form(...),
    current_user: str = Depends(get_current_user)
):
    """
    Checks the status of a chunked upload session.
    
    Args:
        session (SessionDep): Database session dependency.
        request (SessionStatusRequest): The session ID to check.
        current_user (str): The current user making the request.
        
    Returns:
        dict: Status information about the session.
    """
    try:
        
        # Check if the session exists in our temporary storage
        TEMP_DIRE = chunk_manager.get_session_dir(session_id)
        if TEMP_DIRE and os.path.exists(TEMP_DIRE):
            # Look for chunk files with any extension (not just .bin)
            chunk_files = sorted(list(Path(TEMP_DIRE).glob("chunk_*.*")))
            
            # Create simplified chunk information with only chunk_number and filename
            chunks_info = []
            for chunk_file in chunk_files:
                chunk_number = int(chunk_file.stem.split('_')[1])
                chunks_info.append({
                    "chunk_number": chunk_number,
                    "filename": chunk_file.name
                })
            
            return {
                "status": True,
                "session_id": session_id,
                "chunks_received": len(chunk_files),
                "processing_complete_status": False,
                "chunks_info": chunks_info,
                "message": f"Upload in progress, {len(chunk_files)} chunks received"
            }
        else:
            # Session not found in temporary storage
            # This could mean either it's completed and cleaned up, or it never existed
            return {
                "status": False,
                "session_id": session_id,
                "message": "Session not found or already processed"
            }
    
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error checking session status: {str(e)}")
        log_args = {
            "session": session_id,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR, 
            "endpoint": "check_upload_status",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise





@router.post("/voice/upload/create-session")
async def create_upload_session(
    db_session: SessionDep,
    request: CreateSessionRequest,
    current_user: str = Depends(get_current_user),
    current_tenant: UUID = Depends(get_current_active_tenant)
):
    """
    Creates a new upload session by inserting an entry into the Audio table.

    Args:
        db_session (SessionDep): Database session dependency.
        request (CreateSessionRequest): Request containing the filename of the audio file to be uploaded.
        current_user (str): The current user making the request.

    Returns:
        dict: Session information including session_id and filename.
    """
    try:
        # Extract filename from request
        filename = request.filename
        
        # Create new Audio entry
        audio_entry = Audios(
            filename=filename,
            status=StatusEnum.UPLOADING,
            created_by=current_user.id,
            tenant_id=current_tenant
        )
        db_session.add(audio_entry)
        db_session.commit()
        db_session.refresh(audio_entry)

        # Use the audio ID as session_id
        session_id = audio_entry.id

        # Create session directory for chunk storage
        user_id = getattr(current_user, "id", "unknown")
        session_dir = Path(os.path.join(TEMP_DIRE, str(user_id), str(session_id)))
        session_dir.mkdir(parents=True, exist_ok=True)

        # Register the session in the chunk manager
        chunk_manager.register_session(session_id, str(session_dir), current_user)

        log_status(logger, logging.INFO, f"Created upload session {session_id} for file '{filename}' by user {user_id}")

        return {
            "status": True,
            "status_code": 201,
            "session_id": session_id,
            "filename": filename,
            "message": "Upload session created successfully"
        }

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error creating upload session for '{filename}': {str(e)}")
        log_args = {
            "session": db_session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "create_upload_session",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise


@router.post("/voice/upload/chunk")
async def upload_chunk(
    db_session: SessionDep,
    chunk_data: UploadFile = File(...),
    chunk_number: int = Form(...),
    total_chunks: int = Form(...),
    session_id: int = Form(...),
    current_user: str = Depends(get_current_user)
):
    """
    Uploads a single chunk of audio data as part of a chunked upload.

    Args:
        db_session (SessionDep): Database session dependency.
        chunk_data (UploadFile): The uploaded chunk file.
        chunk_number (int): The sequence number of this chunk (1-based).
        total_chunks (int): The total number of chunks expected.
        session_id (str): The session ID returned from create-session endpoint.
        current_user (str): The current user making the request.

    Returns:
        dict: Status information about the chunk upload.

    Workflow:
        - Validates session_id exists and belongs to current user
        - Stores chunk data for the session
        - Automatically updates status to  when final chunk is uploaded

    Note: session_id must be obtained from /voice/upload/create-session endpoint first.
    """
    try:
        # Validate chunk number and total chunks
        if chunk_number < 1:
            raise BadRequestException("Chunk number must be at least 1")

        if total_chunks < 1:
            raise BadRequestException("Total chunks must be at least 1")

        if chunk_number > total_chunks:
            raise BadRequestException(f"Chunk number ({chunk_number}) exceeds total chunks ({total_chunks})")

        # Validate session_id parameter
        if not session_id :
            raise BadRequestException("session_id parameter is required and cannot be empty")

        # Find the Audio entry by session_id (which is the Audio table's primary key)
        try:
            audio_id = int(session_id)
        except ValueError:
            raise BadRequestException(f"Invalid session_id format: {session_id}")

        audio_entry = db_session.query(Audios).filter(
            Audios.id == audio_id,
            Audios.created_by == current_user.id,
            Audios.status == StatusEnum.UPLOADING
        ).first()

        if not audio_entry:
            raise BadRequestException(f"Invalid session_id or session not in progress: {session_id}")

        # Read chunk data
        chunk_bytes = await chunk_data.read()

        # Process the chunk with filename for proper extension handling
        result = await process_chunked_audio(chunk_bytes, chunk_number, total_chunks, int(session_id), audio_entry.filename)

        # Check if this is the final chunk and automatically update status
        if chunk_number == total_chunks:
            # All chunks uploaded, update status to UPLOADED
            audio_entry.status = StatusEnum.UPLOADED
            audio_entry.updated_by = current_user.id
            db_session.commit()

            log_status(logger, logging.INFO, f"All chunks uploaded for session {session_id} (file: '{audio_entry.filename}'). Status automatically updated to UPLOADED.")

        return result
    
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error processing chunk {chunk_number} for session {session_id}: {str(e)}")
        log_args = {
            "status": True,
            "session": db_session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "upload_chunk",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise



@router.post("/audio/transcription/start", response_model=AudioStartTranscriptionResponse)
async def start_audio_transcription(
    session_id: int = Form(...),
    current_user: str = Depends(get_current_user),
    current_tenant: UUID = Depends(get_current_active_tenant)
):
    """
    Handles the initiation of an audio transcription task for an existing session.

    This endpoint starts transcription for an existing audio session (e.g., from chunk upload).
    The session must already exist and have audio data ready for transcription.

    Args:
        session (SessionDep): Database session dependency.
        session_id (int): The ID of the existing audio session to start transcription for.
        current_user (str): The currently authenticated user, injected via dependency.

    Returns:
        AudioStartTranscriptionResponse: The response from the transcription task initiation.

    Raises:
        GenericException: For known, handled exceptions.
        Exception: For any other exceptions, which are logged to the database before being re-raised.
    """
    try:
        # Start transcription for the existing session
        result = await start_audio_task_for_session(session_id, current_user,current_tenant)
 

        return AudioStartTranscriptionResponse(
            status=result["status"],
            session_id=result["session_id"],
            message=result["message"]
        )

    except GenericException:
        raise
    except Exception as e:
        log_status(logger, logging.ERROR, f"Unexpected error in start_audio_transcription: {str(e)}")
        # Log the exception to the database
        log_args = {
            "session": session_id,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "start_audio_transcription",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise

@router.post("/audio/transcription/captions", response_model=AudioGetCaptionOutput)
async def get_audio_caption_route(
    session: SessionDep,
    data: AudioGetCaptionInput,
    current_user: str = Depends(get_current_user)
):
    """
    Handles the retrieval of audio captions for a given input.
    This mirrors the video transcription captions endpoint pattern.

    Args:
        session (SessionDep): Database session dependency.
        data (AudioGetCaptionInput): Input data required to fetch captions.
        current_user (str, optional): The currently authenticated user, injected via dependency.

    Returns:
        AudioGetCaptionOutput: The result of the `get_audio_caption` function, containing the requested captions.

    Raises:
        Exception: If an error occurs during caption retrieval, logs the exception and re-raises it.
    """
    try:
        result = await get_audio_caption(data)
        return AudioGetCaptionOutput(
            data=result["data"],
            status=result["status"]
        )
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error retrieving audio captions: {str(e)}")
        # Log the exception to the database
        log_args = {
            "session": session,
            "exc": e.detail if hasattr(e, 'detail') else str(e),
            "status_code": e.status_code if hasattr(e, 'status_code') else status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "/audio/transcription/captions",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise
