import logging
import time
import re
from fastapi import status, <PERSON>, APIRouter, Depends
from config import setup_logging
from app.exceptions import GenericException, BadRequestException
from app.api.services.convert_text_service import convertText
from app.models import ConvertTextResponse, ConvertTextRequest, User
from app.exception_log import log_status
from app.api.deps import get_current_user, get_current_active_tenant

logger = setup_logging("convert_text")

router = APIRouter(tags=["convert_text"])


def validate_and_sanitize_arabic_text(text: str) -> str:
    """
    Validate and sanitize Arabic text input for security and performance.

    Args:
        text: Raw input text

    Returns:
        Sanitized text

    Raises:
        BadRequestException: If validation fails
    """
    if not text or not text.strip():
        raise BadRequestException(detail="Arabic text cannot be empty")

    # Remove potential harmful characters but preserve Arabic text
    text = re.sub(r'[<>"\'\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

    # Limit length for performance and security
    max_length = 10000
    if len(text) > max_length:
        text = text[:max_length]
        log_status(logger, logging.WARNING, f"Text truncated to {max_length} characters")

    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text.strip())

    # Validate that we still have content after sanitization
    if not text:
        raise BadRequestException(detail="Invalid Arabic text after sanitization")

    return text


def log_performance_metrics(processing_time: float, text_length: int, word_count: int, user_id: int):
    """Log performance metrics for monitoring"""
    chars_per_second = text_length / processing_time if processing_time > 0 else 0
    words_per_second = word_count / processing_time if processing_time > 0 else 0

    log_status(logger, logging.INFO,
              f"Performance metrics - User: {user_id}, "
              f"Time: {processing_time:.3f}s, "
              f"Chars: {text_length}, Words: {word_count}, "
              f"Speed: {chars_per_second:.1f} chars/s, {words_per_second:.1f} words/s")


@router.post("/text/convert", response_model=ConvertTextResponse)
async def convert_arabic_text(
    data: ConvertTextRequest = Body(...),
    current_user: User = Depends(get_current_user),
    current_tenant: str = Depends(get_current_active_tenant)
):
    """
    Conversion of Arabic text into word-root pairs using original logic.

    Args:
        data (ConvertTextRequest): Request body containing the "arabic_text" key with the Arabic text to convert.
        current_user (User): The currently authenticated user (dependency).

    Returns:
        ConvertTextResponse: A response model containing:
        - "status": Indicates the success or failure of the operation.
        - "data": A list of dictionaries, each with "word" and "root" keys representing the word and its root.

    Raises:
        BadRequestException: If the input validation fails.
        GenericException: If an error occurs during processing.
    """

    start_time = time.time()

    try:
        log_status(logger, logging.INFO, f"User {current_user.id} converting Arabic text")

        # Validate and sanitize input
        arabic_text = validate_and_sanitize_arabic_text(data.arabic_text)

        # Log processing info
        word_count = len(arabic_text.split())
        log_status(logger, logging.INFO,
                  f"Processing {len(arabic_text)} chars, {word_count} words")

        # Use original logic with validated input
        result = await convertText(arabic_text)
        processing_time = time.time() - start_time

        # Log performance metrics
        log_performance_metrics(processing_time, len(arabic_text), word_count, current_user.id)

        return result

    except BadRequestException:
        # Re-raise validation errors as-is
        raise

    except GenericException as e:
        # Log and re-raise application errors
        processing_time = time.time() - start_time
        log_status(logger, logging.WARNING,
                  f"Application error after {processing_time:.3f}s: {e.detail}")
        raise

    except MemoryError:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Memory error after {processing_time:.3f}s - text too large")
        raise GenericException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="Text too large to process"
        )

    except TimeoutError:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Timeout error after {processing_time:.3f}s")
        raise GenericException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Text processing timed out"
        )

    except Exception as e:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Unexpected error after {processing_time:.3f}s: {type(e).__name__}: {e}")

        # Don't expose internal error details to client
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Text conversion service temporarily unavailable"
        )