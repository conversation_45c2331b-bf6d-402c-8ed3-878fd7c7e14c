from typing import Dict, <PERSON>, Tuple, Union
import logging
import time
from fastapi import status
from config import setup_logging
from app.utils import find_word_in_dictionary
from app.exceptions import GenericException
from app.exception_log import log_status

logger = setup_logging("convert_text")

async def convertText(arabic_text: str) -> Dict[str, Union[bool, List[Dict[str, str]]]]:
    """
    Conversion of Arabic text to word-root pairs using original logic.

    Args:
        arabic_text (str): The Arabic text to be processed.

    Returns:
        Dict[str, Union[bool, List[Dict[str, str]]]]:
            A dictionary with the following structure:
            {
                "status": bool,
                "data": List[Dict[str, str]]
            }
            The "status" key indicates the success or failure of the operation.
            The "data" key contains a list of dictionaries, where each dictionary
            has two keys: "word" and "root", both of which have string values.

    Raises:
        GenericException: If an error occurs while processing the Arabic text.
    """
    start_time = time.time()

    try:
        # Input validation
        if not arabic_text or not arabic_text.strip():
            raise GenericException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Arabic text cannot be empty"
            )

        # Limit text length for performance
        max_length = 10000
        if len(arabic_text) > max_length:
            arabic_text = arabic_text[:max_length]
            log_status(logger, logging.WARNING, f"Text truncated to {max_length} characters")

        # Direct processing - no async overhead for small text operations
        list_of_word_and_root: List[Tuple[str, str]] = find_word_in_dictionary(arabic_text)

        # Ultra-fast response formatting - direct list building
        data = {"word": [], "root": []}
        for word, root in list_of_word_and_root:
            data["word"].append(word)
            data["root"].append(root)

        processing_time = time.time() - start_time
        log_status(logger, logging.INFO, f"Text processing completed in {processing_time:.3f}s")

        return {"status": True, "data": [data]}

    except GenericException:
        raise
    except Exception as error:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR, f"Text processing failed after {processing_time:.3f}s: {error}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Text processing failed: {error.__class__.__name__}"
        )
