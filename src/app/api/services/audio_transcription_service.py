import logging
import os
import subprocess
from pathlib import Path
from fastapi import UploadFile, status as http_status

from app.api.exceptions.upload_voice_exception import AudioConversionError
from app.background_task.task import audio_transcription_task

from app.models import (
    StatusEnum,
    filter_audio_sessions,
    update_audio_status,
    check_audio_status,
    Audios,
)
import time
from app.core.config import TEMP_DIR1

from config import setup_logging
import shutil
from app.exceptions import BadRequestException, GenericException
from app.exception_log import log_status
import tempfile
import uuid
from pathlib import Path
from app.api.services.upload_voice_service import chunk_manager
logger = setup_logging("audio_transcription")


async def assemble_audio_chunks_for_session(session_id: int, filename: str) -> str:
    """
    Assemble audio chunks for a session into a single audio file.

    Args:
        session_id (int): The session ID
        filename (str): The original filename

    Returns:
        str: Path to the assembled audio file
    """
    

    try:
        log_status(logger, logging.INFO, f"Starting chunk assembly for session_id {session_id}")

        # Get the chunk directory for this session
        temp_dir = chunk_manager.get_session_dir(session_id)

        if not temp_dir or not os.path.exists(temp_dir):
            raise Exception(f"No chunks found for session {session_id}")

        # Determine file extension from filename
        file_extension = Path(filename).suffix or ".bin"

        # Get all chunks and sort them by number
        pattern = f"chunk_*{file_extension}"
        chunk_files = sorted(Path(temp_dir).glob(pattern))
        # Fallback to .bin if no chunks found with original extension
        if not chunk_files:
            chunk_files = sorted(Path(temp_dir).glob("chunk_*.bin"))

        if not chunk_files:
            raise Exception(f"No chunks found for session {session_id}")

        log_status(logger, logging.INFO, f"Found {len(chunk_files)} chunks for session_id {session_id}")

        # Create a temporary file for the complete audio
        temp_audio_dir = Path(tempfile.gettempdir()) / "audio_processing"
        temp_audio_dir.mkdir(exist_ok=True)

        # Join all chunks into a single file with proper extension
        joined_file_path = temp_audio_dir / f"audio_{session_id}_{uuid.uuid4().hex}{file_extension}"
        log_status(logger, logging.INFO, f"Assembling chunks into: {joined_file_path}")
        

        with open(joined_file_path, "wb") as outfile:
            for chunk_file in chunk_files:
                with open(chunk_file, "rb") as infile:
                    outfile.write(infile.read())

        # Log file size for debugging
        file_size = joined_file_path.stat().st_size
        log_status(logger, logging.INFO, f"Assembled audio file size: {file_size} bytes")

        # Clean up chunk files
        chunk_manager.remove_session(str(session_id))
        log_status(logger, logging.INFO, f"Cleaned up chunk files for session {session_id}")
        return joined_file_path

    except Exception as error:
        log_status(logger, logging.ERROR, f"Error assembling chunks for session {session_id}: {str(error)}")
        # Clean up chunk files on error
        try:
            chunk_manager.remove_session(str(session_id))
        except:
            pass
        raise error


async def start_audio_task_for_session(session_id: int, current_user, current_tenant) -> dict:
    """
    Start audio transcription task for an existing session.

    This function starts transcription for an existing audio session that already
    has audio data (e.g., from chunk upload workflow).

    Args:
        session_id (int): The ID of the existing audio session
        current_user: The current user object

    Returns:
        dict: Response containing status and session_id
    """
    try:
        log_status(logger, logging.INFO, f"Starting audio transcription for existing session_id: {session_id}")

        # Verify the session exists and belongs to the user
        from app.models import SessionLocal
        db = SessionLocal()
        try:
            audio_entry = db.query(Audios).filter(
                Audios.id == session_id,
                Audios.created_by == current_user.id
            ).first()
            if not audio_entry:
                raise GenericException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail=f"Audio session {session_id} not found or access denied"
                )

            # Check if session is in the right status for transcription
            if audio_entry.status not in [StatusEnum.UPLOADED, StatusEnum.QUEUED]:
                return {
                    "status": False,
                    "session_id": session_id,
                    "message": f"Audio session is in {audio_entry.status.value} status. Expected UPLOADED or QUEUED."
                }

            # Update status to QUEUED if it's UPLOADED
            if audio_entry.status == StatusEnum.UPLOADED:
                update_audio_status(session_id, "QUEUED")
                log_status(logger, logging.INFO, f"Updated session {session_id} status from UPLOADED to QUEUED")

        finally:
            db.close()

        # Now we need to assemble chunks and trigger the actual transcription task
            # Step 1: Assemble chunks into a single audio file
            assembled_audio_path = await assemble_audio_chunks_for_session(session_id, audio_entry.filename)
            
            if not assembled_audio_path:
                raise Exception("Failed to assemble audio chunks")

            log_status(logger, logging.INFO, f"Successfully assembled chunks for session {session_id}: {assembled_audio_path}")

        # Step 1.5: Convert to WAV format if needed (like in video transcription)
        assembled_path = Path(assembled_audio_path)

        # Create final WAV path for the destination
       # TEMP_DIR1 = Path("/home/<USER>/audio_file")
        #wav_filename = f"{session_id}_{assembled_path.stem}.wav"
        #destination_path = TEMP_DIR1 / wav_filename
        audio_path = Path(os.path.join(TEMP_DIR1, f"{session_id}.wav"))
        audio_path.parent.mkdir(parents=True, exist_ok=True)

       
        if assembled_path.suffix.lower() != ".wav":
            log_status(logger, logging.INFO, f"Converting {assembled_path.name} to WAV format for VAD compatibility")

            try:
                # Read the input file contents
                with open(assembled_path, "rb") as f:
                    input_contents = f.read()

                # Convert the audio stream to WAV format using ffmpeg with proper VAD parameters
                process = subprocess.Popen(
                    [
                        "ffmpeg",
                        "-i",
                        "pipe:0",
                        "-f",
                        "wav",
                        "-ar", "16000",  # Sample rate for VAD compatibility
                        "-ac", "1",      # Mono channel for VAD compatibility
                        "pipe:1",
                    ],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                )

                # Prevent ffmpeg from hanging by setting a timeout
                try:
                    output, error = process.communicate(input=input_contents, timeout=60)
                except subprocess.TimeoutExpired:
                    process.kill()
                    raise AudioConversionError("FFmpeg conversion timed out")

                if process.returncode != 0:
                    error_msg = error.decode() if error else "Unknown FFmpeg error"
                    log_status(logger, logging.ERROR, f"FFmpeg error: {error_msg}")
                    raise AudioConversionError("FFmpeg conversion unsuccessful")

                log_status(logger, logging.INFO, "FFmpeg conversion to WAV successful")

                # Write the converted WAV data directly to destination
                with open(audio_path, "wb") as f:
                    f.write(output)

                # Clean up the original non-WAV file
                if assembled_path.exists():
                    assembled_path.unlink()

            except Exception as conv_error:
                log_status(logger, logging.ERROR, f"Audio conversion failed: {str(conv_error)}")
                raise Exception(f"Failed to convert audio to WAV format: {str(conv_error)}")
        else:
            log_status(logger, logging.INFO, "Audio is already in WAV format")
            # Copy WAV file directly to destination
            shutil.copy2(assembled_path, audio_path)

        # Step 2: Trigger the actual Celery background task
        log_status(logger, logging.INFO, f"Triggering audio_transcription_task for session {session_id}")

        # Verify the audio file exists and has content
        if audio_path.exists():
            log_status(logger, logging.INFO, f"Audio file ready for transcription: {audio_path} (size: {audio_path.stat().st_size} bytes)")
        else:
            raise Exception(f"Converted audio file not found at: {audio_path}")

        # Clean up the temporary assembled file if it still exists
        if assembled_path.exists():
            assembled_path.unlink()
            log_status(logger, logging.INFO, f"Cleaned up temporary file: {assembled_path}")

        # Trigger the Celery task with the verified file path
        user_id = current_user.id
        result = audio_transcription_task.delay(str(audio_path), session_id, user_id, current_tenant)
        result.forget()  # We don't need to track the result

        log_status(logger, logging.INFO, f"Successfully triggered audio transcription task for session_id: {session_id}")

        return {
            "status": True,
            "session_id": session_id,
            "message": "Audio transcription started successfully for existing session"
        }

    except GenericException:
        raise
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error starting transcription for session {session_id}: {str(e)}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start transcription for session {session_id}: {str(e)}"
        )


async def get_audio_caption(data: dict) -> dict:
    """
    Retrieve captions for an audio session starting from a specific time.

    This function mirrors the video get_caption pattern but for audio transcriptions.
    It processes the input data, filters sessions based on the provided
    session ID and start time, and returns the corresponding captions.

    Parameters:
    -----------
    data : dict
        A dictionary containing:
        - 'session_id': int, the unique identifier of the audio session
        - 'start_time': str or datetime, the starting time for caption retrieval

    Returns:
    --------
    dict
        A dictionary containing:
        - 'data': list of caption data or an empty list if no captions are found
        - 'status': bool or str, indicating the status of the operation

    Raises:
    -------
    BadRequestException
        If the required 'session_id' or 'start_time' keys are missing from the input data.
    GenericException
        If there's an error during the caption retrieval process or if the audio status check fails.
    """
    try:
        session_id = data.session_id
        start_time = data.start_time
    except KeyError as e:
        raise BadRequestException(f"{e.__class__.__name__}: {e}. session_id and start_time required")

    try:
        log_status(logger, logging.INFO, f"filtering audio sessions for {session_id} and {start_time}")
        response = await filter_audio_sessions(session_id, start_time)

        log_status(logger, logging.INFO, "Filtering audio data")

        if len(response) < 1:
            status = await check_audio_status(session_id)
            status_bool = status[0]
            status_str = status[1]
            log_status(logger, logging.INFO, f"Status from check_audio_status is {status}")
            if status_bool:
                return {"data": [], "status": status_str}
            else:
                raise GenericException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=status_str,
                )
        else:
            return {"data": response, "status": True}
    except GenericException:
        raise 
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error while filtering audio data: {e}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{e.__class__.__name__}: {e}. Unable to retrieve audio transcript",
        )
