import logging
import os
import subprocess
from fastapi.responses import JSONResponse
from fastapi import status as http_status
from pathlib import Path
from sqlmodel import Session
from fastapi import UploadFile, status as http_status
from typing import BinaryIO, Optional
from app.models import Videos,GetCaptionInput
from app.api.deps import SessionDep
from sqlmodel import select
from pydantic import ValidationError

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

import hashlib
from app.api.exceptions.upload_voice_exception import AudioConversionError
from app.background_task.task import google_transcription
from app.background_task.celery import app as celery_app
from app.core.audio_converter import get_audio_converter
from app.core.audio_config import get_memory_config
from config import setup_logging

from app.models import (
    StatusEnum,
    filter_sessions,
    video_exists,
    add_video_record,
    update_videos,
    check_video_status,
)

from app.core.config import (
    # setup_logging,
    TEMP_DIR,
)

from app.exceptions import BadRequestException, GenericException
from app.exception_log import log_status
from uuid import UUID

logger = setup_logging("video_transcription")


def check_celery_workers_available() -> bool:
    """
    Check if Celery workers are available to process tasks.

    Returns:
        bool: True if workers are available, False otherwise
    """
    try:
        # Get active workers
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()

        if active_workers is None:
            log_status(logger, logging.WARNING, "No response from Celery workers - workers may not be running")
            return False

        if len(active_workers) == 0:
            log_status(logger, logging.WARNING, "No active Celery workers found")
            return False

        log_status(logger, logging.INFO, f"Found {len(active_workers)} active Celery workers")
        return True

    except Exception as e:
        log_status(logger, logging.ERROR, f"Failed to check Celery workers: {e}")
        return False


def get_celery_queue_info() -> dict:
    """
    Get information about Celery queues and pending tasks.

    Returns:
        dict: Queue information including pending task counts
    """
    try:
        inspect = celery_app.control.inspect()

        # Get queue information
        active_queues = inspect.active_queues()
        reserved_tasks = inspect.reserved()

        queue_info = {
            "active_queues": active_queues or {},
            "reserved_tasks": reserved_tasks or {},
            "total_pending": 0
        }

        # Count total pending tasks
        if reserved_tasks:
            for worker, tasks in reserved_tasks.items():
                queue_info["total_pending"] += len(tasks)

        return queue_info

    except Exception as e:
        log_status(logger, logging.ERROR, f"Failed to get queue info: {e}")
        return {"error": str(e)}


# Security constants
ALLOWED_VIDEO_MIME_TYPES = {
    'video/mp4', 'video/quicktime', 'video/x-msvideo',
    'video/x-matroska', 'video/webm', 'audio/wav'
}

ALLOWED_EXTENSIONS = {'.mp4', '.mov', '.avi', '.mkv', '.wav', '.webm'}

MAX_FILE_SIZE_MB = 100
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# Chunk size for streaming file reads (1MB)
CHUNK_SIZE = 1024 * 1024



def validate_file_extension(filename: str) -> bool:
    """Validate file extension against allowed types."""
    if not filename:
        return False

    file_ext = Path(filename).suffix.lower()
    return file_ext in ALLOWED_EXTENSIONS


def validate_file_mime_type(file_content: bytes) -> bool:
    """Validate file MIME type using magic numbers."""
    if not MAGIC_AVAILABLE:
        log_status(logger, logging.WARNING, "python-magic not available, skipping MIME type validation")
        return True  # Fall back to extension validation only

    try:
        mime_type = magic.from_buffer(file_content, mime=True)
        return mime_type in ALLOWED_VIDEO_MIME_TYPES
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error detecting MIME type: {e}")
        return False


def calculate_file_hash(content: bytes) -> str:
    """Calculate SHA-256 hash of file content for deduplication."""
    return hashlib.sha256(content).hexdigest()


async def validate_file_security(file: UploadFile) -> tuple[bytes, str]:
    """
    Comprehensive file validation including size, type, and content checks.

    Returns:
        tuple: (file_content, file_hash)

    Raises:
        BadRequestException: If validation fails
    """
    # Validate filename
    if not file.filename:
        raise BadRequestException("Filename is required")

    # Validate file extension
    if not validate_file_extension(file.filename):
        raise BadRequestException(
            f"Unsupported file type. Allowed extensions: {', '.join(ALLOWED_EXTENSIONS)}"
        )

    # Read file content in chunks to avoid memory issues
    content_chunks = []
    total_size = 0

    try:
        while chunk := await file.read(CHUNK_SIZE):
            total_size += len(chunk)

            # Check size limit during reading
            if total_size > MAX_FILE_SIZE_BYTES:
                raise BadRequestException(f"File too large. Maximum size: {MAX_FILE_SIZE_MB}MB")

            content_chunks.append(chunk)

        # Combine chunks
        file_content = b''.join(content_chunks)

        # Validate MIME type using magic numbers
        if not validate_file_mime_type(file_content):
            raise BadRequestException(
                f"Invalid file type. Allowed types: {', '.join(ALLOWED_VIDEO_MIME_TYPES)}"
            )

        # Calculate file hash for deduplication
        # file_hash = calculate_file_hash(file_content)

        # return file_content, file_hash
        return file_content

    except Exception as e:
        if isinstance(e, BadRequestException):
            raise
        log_status(logger, logging.ERROR, f"Error validating file: {e}")
        raise BadRequestException("File validation failed")
    finally:
        # Reset file pointer for potential reuse
        await file.seek(0)


# def create_temp_file(content: bytes, session_id: int, suffix: str = ".wav") -> Path:
#     """
#     Create a temporary file for background task processing.

#     Note: The file cleanup is handled by the background task itself to avoid
#     race conditions where the file is deleted before the async task can process it.

#     Args:
#         content: File content bytes
#         session_id: Session identifier for unique naming
#         suffix: File extension

#     Returns:
#         Path: Temporary file path
#     """
#     try:
#         # Create secure temporary file
#         temp_file_path = Path(TEMP_DIR) / f"{session_id}{suffix}"

#         # Ensure temp directory exists
#         temp_file_path.parent.mkdir(parents=True, exist_ok=True)

#         # Write content to file
#         with open(temp_file_path, 'wb') as f:
#             f.write(content)

#         # Set secure file permissions (owner read/write only)
#         temp_file_path.chmod(0o600)

#         log_status(logger, logging.INFO, f"Created temporary file: {temp_file_path}")
#         return temp_file_path

#     except Exception as e:
#         log_status(logger, logging.ERROR, f"Error creating temporary file: {e}")
#         raise GenericException(
#             status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Failed to create temporary file"
#         )



async def start_task(video_link: str, file: UploadFile,user_id: UUID, current_tenant: UUID) -> dict:
    try:
        log_status(logger, logging.INFO, "Starting start_task")


        # Validate file security first
        # file_content, file_hash = await validate_file_security(file)
        file_content = await validate_file_security(file)
        log_status(logger, logging.INFO, f"File validation passed...")

        session_id = video_exists(video_link)
        if session_id == -1:
            session_id = add_video_record(video_link, StatusEnum.QUEUED, user_id, current_tenant)

            # Validate session_id after attempting to add
            if not session_id or session_id == -1:
                log_status(logger, logging.ERROR, f"Failed to create video record for {video_link}")
                raise GenericException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create video record. Unable to process video.",
                )
            log_status(logger, logging.INFO, f"Created new video record with session_id: {session_id}")
        else:
            status, _ = await check_video_status(session_id)
            if status:
                return {
                    "status": True,
                    "session_id": session_id,
                }

        # Create temporary file for background task processing
        audio_path = Path(os.path.join(TEMP_DIR, f"{session_id}.wav"))
        # create_temp_file(file_content, session_id, ".wav")

        # Get optimized audio converter
        audio_converter = get_audio_converter()
        memory_config = get_memory_config()

        if os.path.splitext(file.filename)[1].lower() != ".wav":
            # Use optimized audio conversion
            log_status(logger, logging.INFO, f"Converting {file.filename} to wav using optimized converter")

            # Check file size to decide conversion strategy
            file_size = len(file_content)
            max_memory_size = 50 * 1024 * 1024  # 50MB threshold
            # memory_config["chunk_size"] * 6000  # Rough threshold 49MB

            if file_size > max_memory_size:
                # Use file-based conversion for large files
                log_status(logger, logging.INFO, f"Large file detected ({file_size} bytes), using file-based conversion")
                converted_path = audio_converter.convert_with_temp_file(file_content, session_id, TEMP_DIR)
                # File is already written to the correct path
                if converted_path != audio_path:
                    # Move file if needed
                    import shutil
                    shutil.move(str(converted_path), str(audio_path))
            else:
                # Use memory-based conversion for smaller files
                log_status(logger, logging.INFO, f"Small file detected ({file_size} bytes), using memory-based conversion")
                converted_audio = audio_converter.convert_to_wav_memory(file_content)

                # Write converted audio to temp file
                with open(audio_path, "wb") as f:
                    f.write(converted_audio)

            log_status(logger, logging.INFO, f"Optimized FFmpeg conversion successful for {file.filename}")

        else:
            # File is already WAV, write directly to temp file
            log_status(logger, logging.INFO, f"File {file.filename} is already WAV format, writing directly")
            with open(audio_path, "wb") as f:
                f.write(file_content)
       
    except GenericException:
        raise
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error while processing audio: {str(e)}")
        if session_id > 0:
            update_videos(session_id, "ERROR")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{e.__class__.__name__}: {e}. Unable to process audio",
        )
    try:
        # Temporarily disable health check to test task queuing
        # if not check_celery_workers_available():
        #     log_status(logger, logging.ERROR, "No Celery workers available - cannot process transcription task")
        #     if session_id > 0:
        #         update_videos(session_id, "ERROR")
        #     raise GenericException(
        #         status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
        #         detail="Transcription service is currently unavailable. Please try again later."
        #     )

        log_status(logger, logging.INFO, "Celery workers available - starting video transcription in Background Task")

        # Simplified task queuing
        log_status(logger, logging.INFO, "Queuing transcription task")
        results = google_transcription.delay(str(audio_path), session_id, user_id, current_tenant)
        results.forget()
        log_status(logger, logging.INFO, "Transcription task queued successfully")

        return {
            "status": True,
            "session_id": session_id,
        }
    except GenericException:
        raise
    except Exception:
        log_status(logger, logging.ERROR, "Unanticipated Error while processing audio")
        if session_id > 0:
            update_videos(session_id, "ERROR")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unanticipated Error while processing audio",
        )
    


async def get_caption(data: dict,session_db: Session) -> dict:
    """
    Retrieve captions for a video session starting from a specific time.

    This function processes the input data, filters sessions based on the provided
    session ID and start time, and returns the corresponding captions. If no captions
    are found, it checks the video status and returns appropriate information.

    Parameters:
    -----------
    data : dict
        A dictionary containing:
        - 'session_id': int, the unique identifier of the video session
        - 'start_time': str or datetime, the starting time for caption retrieval

    Returns:
    --------
    dict
        A dictionary containing:
        - 'data': list of caption data or an empty list if no captions are found
        - 'status': bool or str, indicating the status of the operation

    Raises:
    -------
    BadRequestException
        If the required 'session_id' or 'start_time' keys are missing from the input data.
    GenericException
        If there's an error during the caption retrieval process or if the video status check fails.

    Notes:
    ------
    - The function logs information about the filtering process and any errors encountered.
    - It uses the check_video_status function to verify the video status when no captions are found.
    """
    try:
        session_id = data.session_id
        start_time = data.start_time
    except KeyError as e:
        log_status(logger, logging.ERROR, f"{e.__class__.__name__}: {e}. Missing 'session_id' or 'start_time' in input data.")
        raise BadRequestException(f"Missing 'session_id' or 'start_time' in input data.")
    try:
        statement = select(Videos).where(Videos.id == session_id)
        result = session_db.exec(statement)
        video = result.first()
        if not video:
            log_status(logger, logging.ERROR, f"Session ID {session_id} not found in Videos table")
            return JSONResponse(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                content={
                    "status": False,
                    "detail": "Session id is not valid"
                }
            )
            

        log_status(logger, logging.INFO, f"filtering sessions for {session_id} and {start_time}")
        response = await filter_sessions(session_id, start_time)

        log_status(logger, logging.INFO, "Filtering data")
        #log_status(logger, logging.INFO, response)

        if len(response) < 1:
            status = await check_video_status(session_id)
            status_bool = status[0]
            status_str = status[1]
            log_status(logger, logging.INFO, f"Status from check_video_status is {status}")
            if status_bool:
                return {"data": [], "status": status_str}
            else:
                raise GenericException(
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=status_str,
                )
        else:
            return {"data": response, "status": True}
    except GenericException:
        raise 
    except Exception as e:
        # logger.error("Error while filtering data")
        log_status(logger, logging.ERROR, f"Error while filtering data: {e}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving the transcript.",
        )
