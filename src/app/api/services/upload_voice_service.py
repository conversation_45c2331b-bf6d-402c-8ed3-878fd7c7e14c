import os
import logging
import subprocess
import io
import tempfile
import time
from pathlib import Path
from typing import List, <PERSON><PERSON>

from pydub import AudioSegment
from fastapi import status as http_status

from config import setup_logging
from app.utils import generate_raw_transcript, generate_raw_transcript_optimized, find_word_in_dictionary
from app.exceptions import GenericException
from app.exception_log import log_status
from app.api.exceptions.upload_voice_exception import AudioConversionError
from threading import Lock
import shutil
from datetime import datetime
from app.exceptions import GenericException, BadRequestException

logger = setup_logging("upload_voice")

# Optimization constants
FFMPEG_TIMEOUT = 60
TEMP_FILE_PREFIX = "audio_"
WAV_FORMAT = "wav"


def is_wav_file(filename: str) -> bool:
    """Check if file is already in WAV format"""
    return os.path.splitext(filename)[1].lower() == ".wav"


def convert_to_wav_optimized(contents: bytes, filename: str) -> bytes:
    """
    Optimized audio conversion to WAV format using FFmpeg.

    Args:
        contents: Audio file contents as bytes
        filename: Original filename for logging

    Returns:
        WAV format audio data as bytes

    Raises:
        AudioConversionError: If conversion fails
    """
    log_status(logger, logging.INFO, f"Converting {filename} to WAV format")

    # Optimized FFmpeg command for faster conversion
    ffmpeg_cmd = [
        "ffmpeg",
        "-i", "pipe:0",           # Input from stdin
        "-f", "wav",              # Output format
        "-acodec", "pcm_s16le",   # Audio codec for compatibility
        "-ar", "16000",           # Sample rate optimization
        "-ac", "1",               # Mono channel for faster processing
        "-y",                     # Overwrite output
        "pipe:1"                  # Output to stdout
    ]

    try:
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Use timeout to prevent hanging
        output, error = process.communicate(input=contents, timeout=FFMPEG_TIMEOUT)

        if process.returncode != 0:
            error_msg = error.decode('utf-8', errors='ignore')
            log_status(logger, logging.ERROR, f"FFmpeg conversion failed: {error_msg}")
            raise AudioConversionError(f"Audio conversion failed: {error_msg}")

        log_status(logger, logging.INFO, f"Successfully converted {filename} to WAV")
        return output

    except subprocess.TimeoutExpired:
        process.kill()
        log_status(logger, logging.ERROR, f"FFmpeg conversion timed out for {filename}")
        raise AudioConversionError("Audio conversion timed out")
    except Exception as e:
        log_status(logger, logging.ERROR, f"FFmpeg conversion error: {e}")
        raise AudioConversionError(f"Audio conversion error: {e}")





async def generate_file_transcript(filename: str, contents: bytes) -> dict:
    """
    Optimized audio file transcription with enhanced performance and error handling.

    This function processes an audio file to generate a transcript with the following optimizations:
    - Efficient audio format detection and conversion
    - Optimized FFmpeg parameters for faster processing
    - Memory-efficient temporary file handling
    - Enhanced error handling and logging

    Args:
        filename (str): Original filename of the uploaded audio file
        contents (bytes): Audio file contents as bytes

    Returns:
        dict: A dictionary containing the status and transcription results:
              {
                  "status": bool,
                  "data": [{"word": str, "root": str}, ...]
              }

    Raises:
        AudioConversionError: If audio conversion fails
        GenericException: If transcription or dictionary lookup fails
    """
    start_time = time.time()

    try:
        log_status(logger, logging.INFO, f"Starting transcription for: {filename}")

        # Step 1: Optimized audio format handling
        is_wav = is_wav_file(filename)

        if not is_wav:
            # Convert to WAV with optimized settings
            conversion_start = time.time()
            wav_data = convert_to_wav_optimized(contents, filename)
            conversion_time = time.time() - conversion_start
            log_status(logger, logging.INFO, f"Audio conversion completed in {conversion_time:.3f}s")
        else:
            wav_data = contents
            log_status(logger, logging.INFO, f"File {filename} already in WAV format")

        # Step 2: Load audio segment directly (we know it's WAV format at this point)
        try:
            audio_segment = AudioSegment.from_file(io.BytesIO(wav_data), format="wav")
        except Exception as e:
            log_status(logger, logging.ERROR, f"Failed to load audio segment: {e}")
            raise AudioConversionError(f"Audio loading failed: {e}")

        # Step 3: Optimized temporary file handling
        with tempfile.TemporaryDirectory(prefix=TEMP_FILE_PREFIX) as temp_dir:
            temp_wav_path = Path(temp_dir) / f"processed_{filename}.wav"

            # Export with optimized settings
            export_start = time.time()
            audio_segment.export(
                str(temp_wav_path),
                format=WAV_FORMAT,
                parameters=["-ar", "16000", "-ac", "1"]  # Optimize for transcription
            )
            export_time = time.time() - export_start
            log_status(logger, logging.INFO, f"Audio export completed in {export_time:.3f}s")

            # Step 4: Generate transcript using optimized version
            transcript_start = time.time()
            log_status(logger, logging.INFO, "Generating transcript using optimized processing")

            # Try optimized version first, fall back to standard if needed
            try:
                transcription = generate_raw_transcript_optimized(str(temp_wav_path))
                log_status(logger, logging.INFO, "Used optimized transcription method")
            except Exception as e:
                log_status(logger, logging.WARNING, f"Optimized transcription failed, using standard: {e}")
                transcription = generate_raw_transcript(str(temp_wav_path))

            transcript_time = time.time() - transcript_start
            log_status(logger, logging.INFO, f"Transcription completed in {transcript_time:.3f}s")

            # Step 5: Dictionary lookup with performance monitoring
            if transcription and transcription.strip():
                dict_start = time.time()
                log_status(logger, logging.INFO, "Processing transcription through dictionary")
                list_of_word_and_root: List[Tuple[str, str]] = find_word_in_dictionary(transcription)
                dict_time = time.time() - dict_start
                log_status(logger, logging.INFO, f"Dictionary processing completed in {dict_time:.3f}s")
            else:
                log_status(logger, logging.WARNING, "Empty transcription received")
                list_of_word_and_root = []

        # Step 6: Optimized response formatting
        data = [{"word": word, "root": root} for word, root in list_of_word_and_root]

        # Performance summary
        total_time = time.time() - start_time
        log_status(logger, logging.INFO,
                  f"Total processing time for {filename}: {total_time:.3f}s, "
                  f"Words found: {len(list_of_word_and_root)}")

        # Return result based on whether words were found
        return {
            "status": len(list_of_word_and_root) > 0,
            "data": data
        }

    except AudioConversionError:
        # Re-raise audio conversion errors as-is
        raise

    except GenericException:
        # Re-raise application errors as-is
        raise

    except Exception as e:
        processing_time = time.time() - start_time
        log_status(logger, logging.ERROR,
                  f"Unexpected error in transcript generation after {processing_time:.3f}s: {type(e).__name__}: {e}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Transcript generation failed: {type(e).__name__}"
        )



# Replace the global dictionary with a thread-safe class
class ChunkManager:
    def __init__(self, cleanup_interval=3600):  # 1 hour default cleanup interval
        self.chunks = {}  # Maps session_id to (temp_dir, last_access_time, user_info)
        self.lock = Lock()
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
    
    def register_session(self, session_id, temp_dir, user_info):
        """Register a new session with its directory and user information"""
        with self.lock:
            self.chunks[session_id] = (temp_dir, time.time(), user_info)
            return temp_dir
    
    def create_session(self, session_id):
        """Legacy method - use register_session for new code"""
        with self.lock:
            if session_id not in self.chunks:
                temp_dir = tempfile.mkdtemp(prefix=f"audio_chunks_{session_id}_")
                self.chunks[session_id] = (temp_dir, time.time(), None)
                return temp_dir
            return self.chunks[session_id][0]
    
    def get_session_dir(self, session_id):
        with self.lock:
            if session_id in self.chunks:
                temp_dir, _, user_info = self.chunks[session_id]
                # Update last access time
                self.chunks[session_id] = (temp_dir, time.time(), user_info)
                return temp_dir
            return None
    
    def get_session_info(self, session_id):
        """Get full session information including directory and user info"""
        with self.lock:
            if session_id in self.chunks:
                return self.chunks[session_id]
            return None
    
    def remove_session(self, session_id):
        with self.lock:
            if session_id in self.chunks:
                temp_dir, _, _ = self.chunks[session_id]
                try:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except Exception:
                    pass  # Already logged in the calling function
                del self.chunks[session_id]
    
    def cleanup_old_sessions(self, max_age=3600):  # 1 hour default max age
        """Remove sessions older than max_age seconds"""
        with self.lock:
            current_time = time.time()
            # Only run cleanup periodically
            if current_time - self.last_cleanup < self.cleanup_interval:
                return
                
            self.last_cleanup = current_time
            sessions_to_remove = []
            
            for session_id, (temp_dir, last_access) in self.chunks.items():
                if current_time - last_access > max_age:
                    try:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        sessions_to_remove.append(session_id)
                        log_status(logger, logging.INFO, f"Cleaned up expired session {session_id}")
                    except Exception as e:
                        log_status(logger, logging.ERROR, f"Error cleaning up expired session {session_id}: {str(e)}")
            
            for session_id in sessions_to_remove:
                del self.chunks[session_id]
    
    def get_all_sessions(self):
        with self.lock:
            return {sid: (path, datetime.fromtimestamp(ts), user) 
                   for sid, (path, ts, user) in self.chunks.items()}

# Create a singleton instance
chunk_manager = ChunkManager()

async def process_chunked_audio(chunk_data: bytes, chunk_number: int, total_chunks: int, session_id: str, filename: str = None) -> dict:
    """
    Processes a single chunk of audio data and stores it for later assembly.

    Args:
        chunk_data (bytes): The binary data of the audio chunk
        chunk_number (int): The sequence number of this chunk
        total_chunks (int): The total number of chunks expected
        session_id (str): A unique identifier for this chunked upload session
        filename (str, optional): Original filename to preserve file extension

    Returns:
        dict: A status response containing session_id and chunk information
    """
    try:
        # Run periodic cleanup of old sessions
        chunk_manager.cleanup_old_sessions()
        
        # Get the session directory
        temp_dir = chunk_manager.get_session_dir(session_id)
        if not temp_dir:
            raise BadRequestException(f"Invalid or expired session ID: {session_id}")
        
        # Ensure the directory exists
        Path(temp_dir).mkdir(parents=True, exist_ok=True)
        
        log_status(logger, logging.INFO, f"Using temp directory for session {session_id}: {temp_dir}")

        # Determine file extension from filename if provided, otherwise use .bin
        if filename:
            file_extension = Path(filename).suffix or ".bin"
        else:
            file_extension = ".bin"

        # Save the chunk with a filename that preserves order and original extension
        chunk_path = Path(temp_dir) / f"chunk_{chunk_number:04d}{file_extension}"
        with open(chunk_path, "wb") as f:
            f.write(chunk_data)

        # Count existing chunks to report progress (use pattern that matches the extension)
        pattern = f"chunk_*{file_extension}"
        existing_chunks = list(Path(temp_dir).glob(pattern))
        
        log_status(logger, logging.INFO, f"Saved chunk {chunk_number}/{total_chunks} for session {session_id}")
        
        return {
            "session_id": session_id,
            "chunk_number": chunk_number,
            "total_chunks": total_chunks,
            "chunks_received": len(existing_chunks),
            "message": f"Chunk {chunk_number}/{total_chunks} uploaded successfully"
        }
    
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error processing chunk {chunk_number} for session {session_id}: {str(e)}")
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process audio chunk: {str(e)}"
        )



