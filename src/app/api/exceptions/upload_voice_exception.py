from fastapi import status, Request
from fastapi.responses import JSONResponse

from app.exceptions import GenericException


class AudioConversionError(GenericException):
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail
        )


async def audio_conversion_exception_handler(
    request: Request, exc: AudioConversionError
):
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": False, "detail": exc.detail},
    )
