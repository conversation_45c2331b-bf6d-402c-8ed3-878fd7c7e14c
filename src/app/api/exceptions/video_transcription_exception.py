
from fastapi import status, Request
from fastapi.responses import JSONResponse

from app.exceptions import GenericException
from app.models import update_videos, update_audio_status, StatusEnum


class VideoDownloadError(GenericException):
    def __init__(self, detail: str, session_id: int):
        super().__init__(status_code=status.HTTP_424_FAILED_DEPENDENCY, detail=detail)
        self.session_id = session_id


class AudioExtractionError(GenericException):
    def __init__(self, detail: str, session_id: int):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail
        )
        self.session_id = session_id


async def video_download_exception_handler(request: Request, exc: VideoDownloadError):
    update_videos(exc.session_id, "ERROR")
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": False, "detail": exc.detail},
    )


async def audio_extraction_exception_handler(
    request: Request, exc: AudioExtractionError
):
    update_videos(exc.session_id, "ERROR")
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": False, "detail": exc.detail},
    )

def google_transcription_exception_handler(session_id: int):
    return update_videos(session_id, "ERROR")

def google_large_audio_transcription_exception_handler(session_id: int):
    return update_audio_status(session_id, StatusEnum.ERROR)