from celery import Celery
from app.core.config import settings
from celery.signals import task_prerun, task_postrun, task_failure, worker_ready
from app.core.production_config import get_celery_settings
import logging

app = Celery("transcription")
celery_config = get_celery_settings()

app.conf.update(
    # broker_url="amqp://localhost",
    broker_url=settings.RABBITMQ_URL,
    result_backend="db+" + str(settings.SQLALCHEMY_DATABASE_URI),

    # Serialization
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    
    # Timezone
    timezone="Asia/Kolkata",
    enable_utc=True,
    
    # Database options
    database_engine_options={"echo": False},  # Disable SQL logging in production
    result_expires=3600,  # 1 hour

    # Task execution
    # resource mgmt, prevents hanging
    task_soft_time_limit=celery_config["task_soft_time_limit"],
    task_time_limit=celery_config["task_time_limit"],

    # ack task completion only after its done
    # Re queue if worker lost/crashes
    task_acks_late=celery_config["task_acks_late"],
    task_reject_on_worker_lost=celery_config["task_reject_on_worker_lost"],

    # Worker configuration
    # at once how many tasks to pull from queue
    worker_prefetch_multiplier=celery_config["worker_prefetch_multiplier"],
    worker_disable_rate_limits=celery_config["worker_disable_rate_limits"],
    worker_max_tasks_per_child=1000,  # Restart worker after 1000 tasks
    worker_max_memory_per_child=500000,  # 500MB memory limit

    # Temporarily disable task routing to use default queue
    # task_routes={
    #     'app.background_task.task.google_transcription': {
    #         'queue': 'transcription_high',
    #         'priority': 9
    #     },
    #     'app.background_task.task.generate_raw_transcript_large_audio': {
    #         'queue': 'transcription_normal',
    #         'priority': 5
    #     }
    # },

    # Use default queue configuration
    # task_default_queue='transcription_normal',
    # task_default_exchange='transcription',
    # task_default_exchange_type='direct',
    # task_default_routing_key='transcription.normal',

    # Retry configuration
    # Prevents overwhelming external APIs (Google Speech)

    task_annotations={
        '*': {
            'rate_limit': '10/m',  # 10 tasks per minute per worker
            'max_retries': celery_config["task_max_retries"],
            'default_retry_delay': celery_config["task_default_retry_delay"],
        },
        'app.background_task.task.google_transcription': {
            'rate_limit': '5/m',  # More conservative for transcription
            'max_retries': 3,
            'default_retry_delay': 120,  # 2 minutes
        },
        'app.background_task.task.google_transcription': {
            'rate_limit': '5/m',  # More conservative for transcription
            'max_retries': 3,
            'default_retry_delay': 120,  # 2 minutes
        }
    },

    # Monitoring
    # detailed monitoring and observability
    worker_send_task_events=True,
    task_send_sent_event=True,

    # Security
    # Prevents Celery workers from taking control of Python's root logger
    # Keeps your application's logging configuration intact
    worker_hijack_root_logger=False,
    worker_log_color=False,
)


# Task monitoring signals
@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Log task start."""
    logging.info(f"Task {task.name} [{task_id}] started with args={args}, kwargs={kwargs}")


@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None,
                        retval=None, state=None, **kwds):
    """Log task completion."""
    logging.info(f"Task {task.name} [{task_id}] completed with state={state}")


@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """Log task failure."""
    logging.error(f"Task {sender.name} [{task_id}] failed: {exception}")


@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Initialize models when worker is ready."""
    try:
        logging.info("Worker ready - ENABLING MODEL PRELOADING for optimized performance")
        from app.core.model_manager import initialize_models
        initialize_models()
        logging.info("Models preloaded successfully - worker ready for optimized processing")
    except Exception as e:
        logging.error(f"Failed to initialize models: {e}")
        logging.error("Worker will continue without model preloading (will load models per task)")
        # Don't raise exception to prevent worker from failing to start



if __name__ == "__main__":
    print("starting celery ...")
    app.start()