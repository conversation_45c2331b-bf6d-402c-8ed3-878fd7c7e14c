import traceback, logging
import gc
import time

from pathlib import Path

import torch
import numpy as np

from silero_vad import VADItera<PERSON>, load_silero_vad, read_audio
from uuid import UUID

from app.exception_log import log_status
from app.core.model_manager import get_model_manager
from app.core.audio_processor import get_audio_processor
from app.core.audio_config import get_vad_config
from app.models import (
    update_videos, add_session_record, StatusEnum, 
    update_audio_status,
    add_audio_transcription_record
    )
from app.utils import Transcript, google_transcribe_speech_chunk, format_transcript
from config import setup_logging
from app.api.exceptions.video_transcription_exception import google_large_audio_transcription_exception_handler, google_transcription_exception_handler

from app.background_task.celery import app

logger = setup_logging("background_task")

def insert_into_db(
    session_id: int, transcript: object, status: str, start_time: float, chunk: int, user_id: UUID, current_tenant: UUID
) -> bool:
    """
    Inserts a formatted transcript into the database for a given session.

    Args:
        session_id (int): The unique identifier for the session.
        transcript (object): The transcript data to be formatted and inserted.
        status (str): The current status of the transcript processing (e.g., "COMPLETED", "ERROR").
        start_time (float): The start time offset to be added to each word's timestamp.
        chunk (int): The chunk number of the transcript being processed.

    Returns:
        bool: True if the insertion and status update were successful, False otherwise.

    Raises:
        Exception: Catches and logs any exceptions that occur during the insertion process.
    """
    try:
        log_status(logger, logging.INFO, "Formatting transcript")
        formatted_transcript = format_transcript(transcript)
        for word in formatted_transcript:
            # if not is_arabic(word["word"]):
            #     logger.debug(f"Skipping {word["word"]} because it is not arabic")
            #     continue

            if not add_session_record(
                session_id,
                word["start"] + start_time,
                word["end"] + start_time,
                word["word"],
                word["root"],
                chunk,
                status,
                user_id,
                current_tenant
            ):
                log_status(logger, logging.ERROR, f"Failed to insert data for {word}")
                # for the final chunk, if the status is not set as completed, the api call from the UI will not stop.
                if status != "COMPLETED":
                    status = "ERROR"
        log_status(logger, logging.INFO, f"Inserted {formatted_transcript} into db")

        if status == "COMPLETED":
            # Only update the videos.status field as "COMPLETED" if Trancript is completed to avoid cases of not retrying in case of error
            log_status(logger, logging.INFO, "Updating Status of video")
            update_videos(session_id)
        return True
    except Exception as error:
        # logger.error("Error while inserting data into db")
        log_status(logger, logging.ERROR, f"CRITICAL ERROR in insert_into_db: {error}")
        log_status(logger, logging.ERROR, f"Exception type: {type(error).__name__}")
        import traceback
        log_status(logger, logging.ERROR, f"Full traceback: {traceback.format_exc()}")
        return False


@app.task(bind=True, max_retries=3, default_retry_delay=60)
def google_transcription(self,audio_file_path: str, session_id: int, user_id: UUID, current_tenant: UUID):

    try:
        log_status(logger, logging.INFO, "Starting optimized google transcription")
        update_videos(session_id, "IN_PROGRESS")
        SAMPLING_RATE = 16000

        audio_file_path = Path(audio_file_path)

        # Try optimized approach with model preloading
        use_optimized = False

        try:
            model_manager = get_model_manager()
            audio_processor = get_audio_processor()
            vad_config = get_vad_config(SAMPLING_RATE)  # Pass sampling rate for dynamic window size
            log_status(logger, logging.INFO, "Optimization modules loaded successfully")

            # Try to get pre-loaded VAD iterator
            try:
                vad_iterator = model_manager.get_vad_iterator(
                    threshold=vad_config["threshold"],
                    sampling_rate=SAMPLING_RATE
                )
                log_status(logger, logging.INFO, "Using pre-loaded models from model manager")
                use_optimized = True

            except Exception as vad_error:
                log_status(logger, logging.WARNING, f"Pre-loaded models not available: {vad_error}")
                log_status(logger, logging.INFO, "Loading models fresh for this task")

                # Load models fresh for this task
                model_manager.load_models()
                vad_iterator = model_manager.get_vad_iterator(
                    threshold=vad_config["threshold"],
                    sampling_rate=SAMPLING_RATE
                )
                use_optimized = True

        except Exception as opt_error:
            log_status(logger, logging.WARNING, f"Optimization failed, using original method: {opt_error}")
            use_optimized = False

        if use_optimized:
            # Optimized path with preloaded models
            log_status(logger, logging.INFO, f"VAD Iterator ready with threshold={vad_config['threshold']}")

            log_status(logger, logging.INFO, "Loading audio with optimization")
            try:
                wav = audio_processor.load_audio_optimized(str(audio_file_path), sampling_rate=SAMPLING_RATE)
                log_status(logger, logging.INFO, f"Audio loaded successfully, length: {len(wav)}")

                # Validate audio quality
                if not audio_processor.validate_audio_quality(wav, SAMPLING_RATE):
                    log_status(logger, logging.WARNING, "Audio quality validation failed, proceeding anyway")

            except Exception as audio_error:
                log_status(logger, logging.ERROR, f"Failed to load audio: {audio_error}")
                raise

            # Get optimized window size from config
            window_size_samples = vad_config["window_size_samples"]
            log_status(logger, logging.INFO, f"Using optimized window size: {window_size_samples}")

        else:
            # Fallback to original method
            log_status(logger, logging.INFO, "Using original model loading method (fallback)")
            silero_vad_model = load_silero_vad(onnx=True)
            vad_iterator = VADIterator(silero_vad_model, sampling_rate=SAMPLING_RATE, threshold=0.3)
            wav = read_audio(str(audio_file_path), sampling_rate=SAMPLING_RATE)
            window_size_samples = 512 if SAMPLING_RATE == 16000 else 256
            log_status(logger, logging.INFO, "Original method completed")

        log_status(logger, logging.INFO, "Initialising global tensor")
        try:
            global_tensor = torch.empty((0,))
            log_status(logger, logging.INFO, "Global tensor initialized successfully")
        except Exception as tensor_error:
            log_status(logger, logging.ERROR, f"Failed to initialize tensor: {tensor_error}")
            raise

        ERROR: bool = False
        log_status(logger, logging.INFO, f"ERROR variable initialized to: {ERROR}")

        # Process audio using optimized chunking
        log_status(logger, logging.INFO, "Starting optimized audio processing")

        for i in range(0, len(wav), window_size_samples):
            try:
                log_status(logger, logging.INFO, f"Iterating through chunk {i}")
                log_status(logger, logging.INFO, f"ERROR variable at start of iteration: {ERROR}")
                chunk = wav[i : i + window_size_samples]
                if len(chunk) < window_size_samples or (i + window_size_samples) == len(
                    wav
                ):
                    log_status(logger, logging.INFO, "Final Chunk")
                    log_status(logger, logging.INFO, f"ERROR variable state: {ERROR}")
                    if ERROR:
                        log_status(logger, logging.INFO, "Updating error due to processing errors")
                        update_videos(session_id, "ERROR")
                    else:
                        log_status(logger, logging.INFO, "Processing completed successfully - updating to completed status")
                        update_videos(session_id)
                    break
                
                log_status(logger, logging.INFO, "Running VAD iterator on chunk")
                try:
                    speech_dict = vad_iterator(chunk, return_seconds=True)
                    log_status(logger, logging.INFO, f"VAD iterator completed successfully")
                except Exception as vad_error:
                    log_status(logger, logging.ERROR, f"VAD iterator failed: {vad_error}")
                    speech_dict = None
                    ERROR = True

                log_status(logger, logging.INFO, "Concatenating global tensor")
                global_tensor = torch.cat((global_tensor, chunk))
                log_status(logger, logging.INFO, f"Speech detection result: {speech_dict}")
                if speech_dict is not None:
                    if "start" in speech_dict.keys():
                        start_time = speech_dict["start"]

                    if any("end" in key for key in speech_dict.keys()):
                        end_time = speech_dict["end"]

                        log_status(logger, logging.INFO, f"Starting google transcription for {start_time} to {end_time}")
                        log_status(logger, logging.INFO, "Transcribing speech chunk with google_transcribe_speech_chunk")

                        try:
                            text = google_transcribe_speech_chunk(
                                np.array(global_tensor), SAMPLING_RATE
                            )
                            log_status(logger, logging.INFO, f"Google transcription completed, result: {text}")
                        except Exception as transcription_error:
                            log_status(logger, logging.ERROR, f"Google transcription failed: {transcription_error}")
                            text = None
                            ERROR = True

                        log_status(logger, logging.INFO, "resetting global_tensor")
                        global_tensor = torch.empty((0,))
                        if not text:
                            continue

                        transcript = Transcript(
                            [{"word": text, "start": start_time, "end": end_time}]
                        )
                        log_status(logger, logging.INFO, "inserting into database")
                        try:
                            db_result = insert_into_db(session_id, transcript, "IN_PROGRESS", 0, i, user_id, current_tenant)
                            log_status(logger, logging.INFO, f"Database insertion result: {db_result}")
                            if not db_result:
                                log_status(logger, logging.ERROR, f"Database insertion returned False - this indicates an error occurred in insert_into_db function")
                                ERROR = True
                        except Exception as db_error:
                            log_status(logger, logging.ERROR, f"Database insertion failed with exception: {db_error}")
                            import traceback
                            log_status(logger, logging.ERROR, f"Database insertion traceback: {traceback.format_exc()}")
                            ERROR = True

            except Exception as error:
                log_status(logger, logging.ERROR, f"Error while transcribing chunk with google_transcription: {error}")
                log_status(logger, logging.ERROR, f"Exception details: {type(error).__name__}: {str(error)}")
                import traceback
                log_status(logger, logging.ERROR, f"Full traceback: {traceback.format_exc()}")
                ERROR = True
                log_status(logger, logging.INFO, f"ERROR variable set to True due to exception")

        # Cleanup based on what was used
        if use_optimized:
            try:
                model_manager = get_model_manager()
                model_manager.reset_vad_states()
                log_status(logger, logging.INFO, "Used optimized cleanup")
            except Exception as cleanup_error:
                log_status(logger, logging.WARNING, f"Optimized cleanup failed, using fallback: {cleanup_error}")
                vad_iterator.reset_states()
        else:
            # Fallback cleanup for original method
            vad_iterator.reset_states()
            if 'silero_vad_model' in locals():
                del silero_vad_model
            log_status(logger, logging.INFO, "Used original cleanup method")

        del wav
        gc.collect()

        # Clean up temporary file
        if audio_file_path.exists():
            audio_file_path.unlink()
            log_status(logger, logging.INFO, f"Cleaned up temporary file: {audio_file_path}")

        log_status(logger, logging.INFO, "Task completed successfully")

    except (ConnectionError, TimeoutError)  as exc:
        log_status(logger, logging.ERROR, f"Error while running google_transcription: {exc} retrying...")
        raise self.retry(exc=exc)
    except Exception as error:
        log_status(logger, logging.ERROR, f"Error while running google_transcription: {error}")

        if audio_file_path.exists():
            audio_file_path.unlink()
        google_transcription_exception_handler(session_id)


def insert_audio_transcription_into_db(
    audio_id: int, transcript: object, status: str, start_time: float, chunk: int, user_id: UUID, current_tenant: UUID
) -> bool:
    """
    Inserts a formatted audio transcript into the database for a given audio session.

    Args:
        audio_id (int): The unique identifier for the audio.
        transcript (object): The transcript data to be formatted and inserted.
        status (str): The current status of the transcript processing (e.g., "COMPLETED", "ERROR").
        start_time (float): The start time offset to be added to each word's timestamp.
        chunk (int): The chunk number of the transcript being processed.

    Returns:
        bool: True if the insertion and status update were successful, False otherwise.

    Raises:
        Exception: Catches and logs any exceptions that occur during the insertion process.
    """
    try:
        log_status(logger, logging.INFO, "Formatting audio transcript")
        formatted_transcript = format_transcript(transcript)
        for word in formatted_transcript:
            # if not is_arabic(word["word"]):
            #     logger.debug(f"Skipping {word["word"]} because it is not arabic")
            #     continue

            if not add_audio_transcription_record(
                audio_id,
                word["start"] + start_time,
                word["end"] + start_time,
                word["word"],
                word["root"],
                chunk,
                StatusEnum(status),  # Convert string to StatusEnum
                user_id,
                current_tenant
            ):
                log_status(logger, logging.ERROR, f"Failed to insert audio transcription data for {word}")
                # for the final chunk, if the status is not set as completed, the api call from the UI will not stop.
                if status != "COMPLETED":
                    status = "ERROR"
        log_status(logger, logging.INFO, f"Inserted {len(formatted_transcript)} audio transcription records into db")

        if status == "COMPLETED":
            # Only update the audio.status field as "COMPLETED" if Transcript is completed to avoid cases of not retrying in case of error
            log_status(logger, logging.INFO, "Updating Status of audio to COMPLETED")
            update_audio_status(audio_id, "COMPLETED")
        return True
    except Exception as error:
        log_status(logger, logging.ERROR, f"Error while inserting audio transcription data into db: {error}")
        return False



@app.task(bind=True, max_retries=3, default_retry_delay=60)
def audio_transcription_task(self, audio_file_path: str, session_id: int, user_id: UUID, current_tenant: UUID):


    try:
        log_status(logger, logging.INFO, "Starting google transcription")
        update_audio_status(session_id, StatusEnum.IN_PROGRESS)
        SAMPLING_RATE = 16000

        audio_file_path = Path(audio_file_path)

        silero_vad_model = load_silero_vad(onnx=True)

        log_status(logger, logging.INFO, "Creating VAD Iterator")
        vad_iterator = VADIterator(
            silero_vad_model, sampling_rate=SAMPLING_RATE, threshold=0.3
        )

        log_status(logger, logging.INFO, "Reading audio")
        wav = read_audio(str(audio_file_path), sampling_rate=SAMPLING_RATE)

        window_size_samples = 512 if SAMPLING_RATE == 16000 else 256

        log_status(logger, logging.INFO, "Initialising global tensor")
        global_tensor = torch.empty((0,))

        ERROR: bool = False

        for i in range(0, len(wav), window_size_samples):
            try:
                log_status(logger, logging.INFO, f"Iterating through chunk {i}")
                chunk = wav[i : i + window_size_samples]
                if len(chunk) < window_size_samples or (i + window_size_samples) == len(
                    wav
                ):
                    log_status(logger, logging.INFO, "Final Chunk")
                    if ERROR:
                        log_status(logger, logging.INFO, "Updating error")
                        update_audio_status(session_id, "ERROR")
                    else:
                        update_audio_status(session_id)
                    break
                
                log_status(logger, logging.INFO, "Running VAD iterator on chunk")
                speech_dict = vad_iterator(chunk, return_seconds=True)

                log_status(logger, logging.INFO, "Concatenating global tensor")
                global_tensor = torch.cat((global_tensor, chunk))
                if speech_dict is not None:
                    if "start" in speech_dict.keys():
                        start_time = speech_dict["start"]

                    if any("end" in key for key in speech_dict.keys()):
                        end_time = speech_dict["end"]

                        log_status(logger, logging.INFO, f"Starting google transcription for {start_time} to {end_time}")
                        log_status(logger, logging.INFO, "Transcribing speech chunk with google_transcribe_speech_chunk")

                        text = google_transcribe_speech_chunk(
                            np.array(global_tensor), SAMPLING_RATE
                        )

                        log_status(logger, logging.INFO, "resetting global_tensor")
                        global_tensor = torch.empty((0,))
                        if not text:
                            continue

                        transcript = Transcript(
                            [{"word": text, "start": start_time, "end": end_time}]
                        )
                        log_status(logger, logging.INFO, "inserting into database")
                        insert_audio_transcription_into_db(session_id, transcript, StatusEnum.IN_PROGRESS, 0, i, user_id, current_tenant)

            except Exception as error:
                log_status(logger, logging.ERROR, f"Error while transcribing chunk with google_transcription: {error}")
                ERROR = True

        vad_iterator.reset_states()
        del silero_vad_model
        del wav
        gc.collect()
        if audio_file_path.exists():
            audio_file_path.unlink()
    except (ConnectionError, TimeoutError)  as exc:
        log_status(logger, logging.ERROR, f"Error while running google_transcription: {exc} retrying...")
        raise self.retry(exc=exc)
    except Exception as error:
        log_status(logger, logging.ERROR, f"Error while running google_transcription: {error}")
        if audio_file_path.exists():
            audio_file_path.unlink()
        audio_transcription_exception_handler(session_id)

def audio_transcription_exception_handler(audio_id: int):
    """
    Handles exceptions that occur during audio transcription processing.

    This function is called when an unrecoverable error occurs during audio transcription.
    It updates the audio status to ERROR and logs the failure.

    Args:
        audio_id (int): The ID of the audio record that failed processing
    """
    try:
        log_status(logger, logging.ERROR, f"Audio transcription failed for audio_id {audio_id}")
        update_audio_status(audio_id, "ERROR")
        log_status(logger, logging.INFO, f"Updated audio status to ERROR for audio_id {audio_id}")
    except Exception as error:
        log_status(logger, logging.ERROR, f"Failed to update audio status to ERROR for audio_id {audio_id}: {str(error)}")

