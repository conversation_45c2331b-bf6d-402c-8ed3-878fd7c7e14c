import logging
import os
import tempfile
import time
import gc
import re
import hashlib
import xml.etree.ElementTree as ET
import numpy as np
import soundfile as sf
from typing import Dict, List, Tuple, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from pathlib import Path
from urllib.parse import urlparse, parse_qs
from app.core import security
# Core imports
import emails  # type: ignore
import jwt
from jinja2 import Template
from jwt.exceptions import InvalidTokenError

# Audio processing imports
import speech_recognition as sr
import wave

# Arabic NLP imports
import libqutrub.classverb as classverb
import libqutrub.verb_const as vconst
from pyarabic.araby import strip_harakat

# App imports
from app.core.config import settings
from app.exceptions import GenericException, BadRequestException
from app.exception_log import log_status
from config import setup_logging, speech_recognizer
from fastapi import status

# VAD imports
from silero_vad import (
    load_silero_vad,
    read_audio,
    get_speech_timestamps,
    save_audio,
)

logger = setup_logging("utils")

# ============================================================================
# EMAIL UTILITIES
# ============================================================================

@dataclass
class EmailData:
    html_content: str
    subject: str

def render_email_template(*, template_name: str, context: dict[str, Any]) -> str:
    """
    Renders an email template with the provided context.
 
    Args:
        template_name (str): The filename of the email template to render.
        context (dict[str, Any]): A dictionary containing variables to be used in the template.
 
    Returns:
        str: The rendered HTML content as a string.
 
    Raises:
        FileNotFoundError: If the specified template file does not exist.
        Exception: If rendering the template fails for any reason.
    """
    template_str = (
        Path(__file__).parent / "email-templates" / "build" / template_name
    ).read_text()
    html_content = Template(template_str).render(context)
    return html_content
 

def send_email(
    *,
    email_to: str,
    subject: str = "",
    html_content: str = "",
) -> None:
    """
    Sends an email using the configured SMTP settings.
 
    Args:
        email_to (str): Recipient email address.
        subject (str, optional): Subject of the email. Defaults to an empty string.
        html_content (str, optional): HTML content of the email body. Defaults to an empty string.
 
    Raises:
        AssertionError: If email configuration is not enabled or missing.
 
    Returns:
        None
    """
    assert settings.emails_enabled, "no provided configuration for email variables"
    message = emails.Message(
        subject=subject,
        html=html_content,
        mail_from=(settings.EMAILS_FROM_NAME, settings.EMAILS_FROM_EMAIL),
    )
    smtp_options = {"host": settings.SMTP_HOST, "port": settings.SMTP_PORT}
    if settings.SMTP_TLS:
        smtp_options["tls"] = True
    elif settings.SMTP_SSL:
        smtp_options["ssl"] = True
    if settings.SMTP_USER:
        smtp_options["user"] = settings.SMTP_USER
    if settings.SMTP_PASSWORD:
        smtp_options["password"] = settings.SMTP_PASSWORD
    response = message.send(to=email_to, smtp=smtp_options)

def generate_test_email(email_to: str) -> EmailData:
    """
    Generates a test email for the specified recipient.
 
    Args:
        email_to (str): The recipient's email address.
 
    Returns:
        EmailData: An object containing the HTML content and subject of the test email.
    """
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)
 
def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    """
    Generates the email content and subject for a password reset email.
 
    Args:
        email_to (str): The recipient's email address.
        email (str): The username or email of the user requesting the password reset.
        token (str): The password reset token to be included in the reset link.
 
    Returns:
        EmailData: An object containing the HTML content and subject for the reset password email.
    """
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    link = f"{settings.FRONTEND_HOST}/reset-password?token={token}"
    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)
 

def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    """Generate password reset email"""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    server_host = settings.server_host
    link = f"{server_host}/reset-password?token={token}"
    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)

def generate_new_account_email(
    email_to: str, username: str, password: str
) -> EmailData:
    """
    Generates an email for a new user account with login credentials.
 
    Args:
        email_to (str): The recipient's email address.
        username (str): The username of the new account.
        password (str): The password for the new account.
 
    Returns:
        EmailData: An object containing the HTML content and subject of the email.
    """
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New account for user {username}"
    html_content = render_email_template(
        template_name="new_account.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": username,
            "password": password,
            "email": email_to,
            "link": settings.FRONTEND_HOST,
        },
    )
    return EmailData(html_content=html_content, subject=subject)

# ============================================================================
# JWT TOKEN UTILITIES
# ============================================================================

def generate_password_reset_token(email: str) -> str:
    """
    Generates a JWT token for password reset purposes for the given email address.
 
    Args:
        email (str): The email address for which to generate the password reset token.
 
    Returns:
        str: A JWT token containing the email as the subject and expiration information.
 
    Raises:
        Any exceptions raised by the JWT encoding process.
 
    Note:
        The token includes an expiration time based on the configured settings.
    """
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.now(timezone.utc)
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm=security.ALGORITHM,
    )
    return encoded_jwt

def verify_password_reset_token(token: str) -> str | None:
    """
    Verifies and decodes a password reset JWT token.
 
    Args:
        token (str): The JWT token to verify.
 
    Returns:
        str | None: The subject ('sub') from the decoded token if verification is successful; 
        otherwise, None if the token is invalid.
    """
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None

# ============================================================================
# ARABIC NLP CORE FUNCTIONS
# ============================================================================

# @dataclass
# class Transcript:
#     word: str
#     start_time: float
#     end_time: float
class Transcript:
    def __init__(self, data: list):
        self.words = data

def read_dictionary(file_path: str) -> Tuple[Dict[str, List[str]], Dict[str, str], Dict[str, str]]:
    """Read and parse the Arabic dictionary file"""
    dictionary = {}
    synonym_lookup = {}
    verb_variants = {}
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            parts = line.split(':')
            if len(parts) == 2:
                word, synonyms = line.strip().split(':')
                synonyms_list = [syn.strip() for syn in synonyms.split('،')]
                dictionary[word.strip()] = synonyms_list
                for synonym in synonyms_list:
                    synonym_lookup[synonym] = word.strip()
                for verb in [word.strip()] + synonyms_list:
                    past_form, present_form = get_verb_variants(verb)
                    if past_form:
                        verb_variants[past_form] = verb
                    if present_form:
                        verb_variants[present_form] = verb
    return dictionary, synonym_lookup, verb_variants

def get_verb_variants(verb):
    try:
        vbc = classverb.VerbClass(verb, True)
        present_form = strip_harakat(vbc.conjugate_tense_pronoun(vconst.TenseFuture, vconst.PronounHuwa))
        past_form = strip_harakat(vbc.conjugate_tense_pronoun(vconst.TensePast, vconst.PronounHuwa))
        return past_form, present_form
    except:
        return None, None

# Global dictionary cache for ultra-fast access
_dictionary_cache = None
_dictionary_loaded = False

def get_dictionary():
    """
    Retrieves the dictionary data with optimized caching.
    Loads once and keeps in memory for subsequent requests.
    """
    global _dictionary_cache, _dictionary_loaded

    # Ultra-fast cache check - only load once per application lifecycle
    if not _dictionary_loaded:
        log_status(logger, logging.INFO, "Loading dictionary into cache...")
        start_time = time.time()
        _dictionary_cache = read_dictionary(settings.DICTIONARY_FILE_PATH)
        load_time = time.time() - start_time
        log_status(logger, logging.INFO, f"Dictionary loaded in {load_time:.3f}s")
        _dictionary_loaded = True
        log_status(logger, logging.INFO, "Dictionary cached for subsequent requests")

    return _dictionary_cache

def preload_dictionary():
    """
    Preload dictionary at application startup for instant first request.
    Call this during app initialization.
    """
    log_status(logger, logging.INFO, "Preloading dictionary at startup...")
    get_dictionary()
    log_status(logger, logging.INFO, "Dictionary preloaded successfully")

# ============================================================================
# ARABIC TEXT PROCESSING UTILITIES
# ============================================================================

def normalize_word(word):
    if word.endswith('ة'):
        return word[:-1] + 'ه'
    elif word.endswith('ه'):
        return word[:-1] + 'ة'
    return word
    

def remove_prefixes(word):
    prefixes = ["وال", "بال", "كال", "فال", "لل", "ال", "و", "ب", "ك", "ف"]
    for prefix in sorted(prefixes, key=lambda x: -len(x)):
        if word.startswith(prefix):
            return word[len(prefix):]
    return word
def remove_suffixes(word):
    suffixes = ["يين", "ون", "ين", "ات", "ة", "ه"]
    for suffix in sorted(suffixes, key=lambda x: -len(x)):
        if word.endswith(suffix):
            return word[:-len(suffix)]
    return word

def normalize_hamzah(word):
    normalized_words = [word]
    if 'أ' in word:
        normalized_words.append(word.replace('أ', 'ا'))
    if 'ا' in word:
        normalized_words.append(word.replace('ا', 'أ'))
    if 'إ' in word:
        normalized_words.append(word.replace('إ', 'ا'))
    return normalized_words

def is_singular_or_plural(word):
    plural_suffixes = ['ات', 'ون', 'ين', 'ان', 'وا']
    return 'plural' if any(word.endswith(suffix) for suffix in plural_suffixes) else 'singular'

# ============================================================================
# MAIN NLP PROCESSING FUNCTION
# ============================================================================

def search_in_input(dictionary: dict, synonym_lookup: dict, verb_variants: dict, input_sentence: str) -> List[Tuple[str, str]]:
    """
    Search for Arabic words/phrases in the input sentence using the dictionary.
    Returns list of (original_phrase, matched_phrase) tuples.
    """
    try:
        found_items = []

        # Optimized character replacement using str.translate (5-10x faster than chained replace)
        translation_table = str.maketrans('أإ،.', 'اا  ')
        input_sentence = input_sentence.translate(translation_table)

        words = input_sentence.split()
        i = 0

        while i < len(words):
            matched = False

            # Try phrases from longest to shortest
            for j in range(len(words), i, -1):
                phrase = ' '.join(words[i:j])

                # 1. Direct match in dictionary
                if phrase in dictionary:
                    found_items.append((phrase, phrase))
                    i = j
                    matched = True
                    break

                # 2. Direct match in synonyms
                elif phrase in synonym_lookup:
                    result = synonym_lookup[phrase]
                    found_items.append((phrase, result))
                    i = j
                    matched = True
                    break

                # 3. Try normalized variants
                for variant in normalize_hamzah(normalize_word(phrase)):
                    if variant in dictionary:
                        found_items.append((phrase, variant))
                        i = j
                        matched = True
                        break
                    elif variant in synonym_lookup:
                        result = synonym_lookup[variant]
                        found_items.append((phrase, result))
                        i = j
                        matched = True
                        break
                if matched:
                    break

                # 4. Try without prefixes
                phrase_no_prefix = remove_prefixes(phrase)
                if phrase_no_prefix in dictionary:
                    found_items.append((phrase, phrase_no_prefix))
                    i = j
                    matched = True
                    break

                # 5. Try without suffixes
                no_suffix = remove_suffixes(phrase)
                if no_suffix in dictionary:
                    found_items.append((phrase, no_suffix))
                    i = j
                    matched = True
                    break
                elif no_suffix in synonym_lookup:
                    result = synonym_lookup[no_suffix]
                    found_items.append((phrase, result))
                    i = j
                    matched = True
                    break

                # 6. Try singular form if plural
                if is_singular_or_plural(phrase) == 'plural':
                    singular_form = remove_suffixes(phrase)
                    if singular_form in dictionary:
                        found_items.append((phrase, singular_form))
                        i = j
                        matched = True
                        break

            if not matched:
                # Fallback: return word with characters joined by commas
                fallback_result = ",".join(character for character in words[i])
                found_items.append((words[i], fallback_result))
                i += 1

        return found_items

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error in search_in_input: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"NLP processing error: {e}"
        )

def find_word_in_dictionary(arabic_text: str) -> List[Tuple[str, str]]:
    """
    Main NLP function to process Arabic text and find words in dictionary.

    Args:
        arabic_text (str): Arabic text to process

    Returns:
        List[Tuple[str, str]]: List of (original_word, matched_word) tuples
    """
    try:
        # Get cached dictionary
        dictionary, synonym_lookup, verb_variants = get_dictionary()

        # Process the text
        result = search_in_input(dictionary, synonym_lookup, verb_variants, arabic_text)
        return result

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error in find_word_in_dictionary: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Dictionary lookup error: {e}"
        )

# ============================================================================
# AUDIO PROCESSING UTILITIES
# ============================================================================

def get_audio_duration(filepath: str) -> float:
    """Get audio file duration in seconds"""
    try:
        with wave.open(filepath, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            duration = frames / float(sample_rate)
            return duration
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error getting audio duration: {e}")
        return 0.0

def get_file_size_mb(filepath: str) -> float:
    """Get file size in megabytes"""
    try:
        size_bytes = os.path.getsize(filepath)
        size_mb = size_bytes / (1024 * 1024)
        return size_mb
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error getting file size: {e}")
        return 0.0

def generate_raw_transcript(audio_file: str) -> str:
    """
    Generate raw transcript from audio file using Google Speech Recognition.

    Args:
        audio_file (str): Path to audio file

    Returns:
        str: Transcribed text
    """
    try:
        log_status(logger, logging.INFO, f"Starting transcription for {audio_file}")
        start_time = time.time()

        # Load VAD model
        model = load_silero_vad()

        # Read audio and get speech timestamps
        wav = read_audio(audio_file, sampling_rate=16000)
        speech_timestamps = get_speech_timestamps(wav, model, sampling_rate=16000)

        if not speech_timestamps:
            log_status(logger, logging.WARNING, f"No speech detected in {audio_file}")
            return ""

        # Process with temporary file handling
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            voice_region_file_path = temp_file.name

        try:
            # Save voice regions to temporary file
            save_audio(voice_region_file_path, wav[speech_timestamps[0]['start']:speech_timestamps[-1]['end']], sampling_rate=16000)

            # Check file size and duration limits
            file_size = os.path.getsize(voice_region_file_path)
            if file_size > 25 * 1024 * 1024:  # 25MB limit
                log_status(logger, logging.WARNING, f"Processed audio too large: {file_size} bytes")
                return ""

            # Load and check duration
            with wave.open(voice_region_file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                duration = frames / float(sample_rate)

                if duration > 300:  # 5 minutes limit
                    log_status(logger, logging.WARNING, f"Audio too long: {duration}s")
                    return ""

                # Record full audio for transcription
                with sr.AudioFile(voice_region_file_path) as source:
                    audio_data = speech_recognizer.record(source)

                # Perform transcription
                transcription = speech_recognizer.recognize_google(audio_data, language='ar')

                processing_time = time.time() - start_time
                log_status(logger, logging.INFO,
                          f"Transcription completed in {processing_time:.2f}s: {len(transcription)} chars")

                # No caching - return result directly
                return transcription

        except sr.UnknownValueError:
            log_status(logger, logging.WARNING, "Google Speech Recognition could not understand audio")
            return ""
        except sr.RequestError as e:
            log_status(logger, logging.ERROR, f"Google Speech Recognition request error: {e}")
            return ""
        finally:
            # Clean up temporary file
            try:
                os.unlink(voice_region_file_path)
            except Exception as e:
                log_status(logger, logging.WARNING, f"Failed to cleanup temp file: {e}")

    except Exception as e:
        log_status(logger, logging.ERROR, f"Error in generate_raw_transcript: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Transcription error: {e}"
        )

# ============================================================================
# OPTIMIZED ALIASES (for backward compatibility)
# ============================================================================

# Optimized versions are just aliases to the main functions for simplicity
find_word_in_dictionary_optimized = find_word_in_dictionary
generate_raw_transcript_optimized = generate_raw_transcript

# No-op cache functions since caching is disabled for better performance
def clear_optimization_caches():
    """No-op function - optimization caches disabled for better performance"""
    log_status(logger, logging.INFO, "Optimization caches disabled - no cache to clear")
    gc.collect()

def clear_transcription_cache():
    """No-op function - transcription caching disabled for performance"""
    log_status(logger, logging.INFO, "Transcription caching disabled - no cache to clear")
    gc.collect()

def get_cache_stats():
    """Get cache statistics (optimization caches disabled)"""
    return {
        "word_cache_size": 0,
        "transcription_cache_size": 0,
        "optimization_mode": "direct_processing",
        "caching_disabled": True
    }

def get_transcription_cache_stats():
    """Get transcription processing statistics (no caching)"""
    return {
        "cache_size": 0,
        "cache_max": 0,
        "usage_percent": 0,
        "caching_mode": "disabled_for_performance"
    }

# ============================================================================
# YOUTUBE UTILITIES (for models.py compatibility)
# ============================================================================

def is_youtube_link(url: str) -> bool:
    """
    Checks if the given URL is a YouTube link.

    Args:
        url (str): The URL to check.

    Returns:
        bool: True if the URL belongs to YouTube (including youtu.be), False otherwise.
    """
    parsed_url = urlparse(url)
    return parsed_url.netloc.lower() in ["www.youtube.com", "youtube.com", "youtu.be"]

def extract_video_id(url: str) -> str:
    """
    Extracts the YouTube video ID from a given URL.
    This function supports various YouTube URL formats, including standard, embed, short, and shorts URLs.
    It parses the URL and attempts to extract the 11-character video ID using query parameters and regular expressions.
    Args:
        url (str): The YouTube URL from which to extract the video ID.
    Returns:
        str: The extracted YouTube video ID if found, otherwise None.
    Raises:
        None: Any exceptions are caught and logged; the function returns None on failure.
    """

    try:
        # Patterns for different types of YouTube URLs
        patterns = [
            r"(?:v=|\/)([0-9A-Za-z_-]{11}).*",  # Standard and embed URLs
            r"(?:youtu\.be\/|youtube\.com\/shorts\/)([0-9A-Za-z_-]{11})",  # Short URLs and YouTube Shorts
            r"(?:embed\/|v\/|vi\/|user\/\w+\/\w+\/|u\/\w+\/|watch\?v=|\&v=|\?v=)([0-9A-Za-z_-]{11})",  # Various other formats
        ]

        # Parse the URL
        parsed_url = urlparse(url)

        # Check if it's a valid URL
        if parsed_url.netloc in ("youtube.com", "www.youtube.com", "youtu.be"):
            # Try to extract ID from URL parameters
            query = parse_qs(parsed_url.query)
            if "v" in query:
                return query["v"][0]

            # Try to match the URL against our patterns
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error extracting video ID from URL {url}: {e}")
        return None

# ============================================================================
# ADDITIONAL UTILITIES (from original utils.py)
# ============================================================================

def set_timing_information(
    transcript_data: List[Tuple[str, str]], start_time, end_time
) -> List[Dict]:
    """Update Timing information for each word according to length

    Args:
        transcript_data (List[Dict]): NLP data
        start_time (_type_): start_time from transcription service
        end_time (_type_): end_time from transcription service

    Returns:
        List[Dict]: List of dict with updated timing
    """
    try:
        log_status(logger, logging.INFO, f"Setting timing information for transcript data")
        out_list = []
        word = ""
        root = ""
        out_dict = {}

        for i, item in enumerate(transcript_data):
            if i != 0:
                word += " ;"
                root += " ;"

            word += item[0]
            root += item[1]
        out_dict["start"] = start_time
        out_dict["end"] = end_time
        out_dict["word"] = word
        out_dict["root"] = root
        out_list.append(out_dict)
        return out_list
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error in set_timing_information: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{e.__class__.__name__}: {e}"
        )

def format_transcript(transcript: Transcript) -> List[Dict]:
    """Format transcript by adding NLP and formatting NLP output to include timing information

    Args:
        transcript (Transcript): Trancript object

    Returns:
        List[Dict]: List of dicts with word, start_time, end_time and root_word
    """
    log_status(logger, logging.INFO, f"Formatting transcript")
    out_transcript: List[Dict] = []
    for item in transcript.words:
        nlp_out = find_word_in_dictionary(item["word"])
        out_transcript.extend(
            set_timing_information(nlp_out, item["start"], item["end"])
        )
    return out_transcript

def google_transcribe_speech_chunk(chunk: np.ndarray, sampling_rate: int):
    """
    Transcribes a given audio chunk using Google Speech Recognition API for Arabic language.

    Args:
        chunk (np.ndarray): The audio data as a NumPy array.
        sampling_rate (int): The sampling rate of the audio data.

    Returns:
        str or None: The transcribed text if recognition is successful, otherwise None.
    """
    # Save the chunk to a temporary WAV file
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_wav_path = os.path.join(temp_dir, "temp_chunk.wav")
        sf.write(temp_wav_path, chunk, sampling_rate)

        # Load the WAV file with speech_recognition
        log_status(logger, logging.INFO, f"Loading the WAV file for transcription: {temp_wav_path}")
        with sr.AudioFile(temp_wav_path) as source:
            audio_data = speech_recognizer.record(source)
            try:
                log_status(logger, logging.INFO, "Recognizing audio data using Google Speech Recognition")
                text = speech_recognizer.recognize_google(audio_data, language="ar")
                return text
            except sr.UnknownValueError:
                return None
            except sr.RequestError as e:
                return None

async def parse_xml_to_json(xml_string: str) -> Dict[str, List[Dict[str, Union[float, str]]]]:
    """
    Parses an XML string containing transcript data into a JSON-compatible dictionary.

    Args:
        xml_string (str): The XML string to parse. Expected to contain 'text' elements with 'start', 'dur', and text content.

    Returns:
        Dict[str, List[Dict[str, Union[float, str]]]]: A dictionary with a single key 'transcript', mapping to a list of dictionaries.
            Each dictionary contains:
                - 'start' (float): The start time of the transcript segment.
                - 'dur' (float): The duration of the transcript segment.
                - 'text' (str): The transcript text.

    Raises:
        BadRequestException: If the XML string is not well-formed or cannot be parsed.
        GenericException: For any other exceptions encountered during parsing.
    """
    try:
        log_status(logger, logging.INFO, "Parsing XML string to JSON format")
        # Parse the XML string
        root = ET.fromstring(xml_string)

        # Initialize the result dictionary
        result = {"transcript": []}

        # Iterate through all 'text' elements
        for text_elem in root.findall(".//text"):
            text_data = {
                "start": float(text_elem.get("start")),
                "dur": float(text_elem.get("dur")),
                "text": text_elem.text,
            }
            result["transcript"].append(text_data)

        return result
    except ET.ParseError as e:
        log_status(logger, logging.ERROR, f"Error parsing XML: {e}")
        raise BadRequestException(f"Invalid XML format: {e}")
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error parsing XML: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{e.__class__.__name__}: {e}"
        )

def hash_audio_file(file_content_bytes: bytes) -> str:
    """    Generates a SHA256 hash for the given audio file content.
    Args:
        file_content_bytes (bytes): The content of the audio file as bytes.
    Returns:
        str: The SHA256 hash of the audio file content as a hexadecimal string.
    """
    try:
        # Use SHA256 for the hash (you can choose other hash algorithms like SHA1, MD5, etc.)
        hash_object = hashlib.sha256()

        # Update the hash object with the passed content
        hash_object.update(file_content_bytes)  # Directly pass the bytes

        # Return the hexadecimal digest of the file
        return hash_object.hexdigest()
    except Exception as e:
        log_status(logger, logging.ERROR, f"Error Hashing audio file: {e}")
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"{e.__class__.__name__}: {e}"
        )
