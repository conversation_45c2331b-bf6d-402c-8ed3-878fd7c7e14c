import uuid, logging
from typing import Any
from datetime import datetime
from sqlmodel import Session, select
from fastapi import status as http_status
from app.core.security import get_password_hash, verify_password
from app.models import Item, ItemCreate, User, UserCreate, UserUpdate, AuthCredential,Token,User<PERSON><PERSON>t,Tenant
from datetime import <PERSON><PERSON><PERSON>
from config import setup_logging
from sqlalchemy.exc import IntegrityError
from app.exceptions import BadRequestException,GenericException
from app.exception_log import log_exception_to_db,log_status
from app.core.config import settings
import traceback
import sys
import inspect
from app.core import security
from sqlalchemy.exc import SQLAlchemyError
from fastapi import status
from fastapi.security import OAuth2PasswordBearer
from app.core.config import settings
from fastapi import APIRouter, Depends, HTTPException
from jose import JWTError
import jwt
from fastapi.responses import JSONResponse
from uuid import UUID

logger = setup_logging("crud")

# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login/access-token")

def create_user(*, session: Session, 
                user_create: UserCreate,
                tenant_id: UUID) -> User:
    try:
        log_status(logger, logging.INFO, f"Starting user creation for: {user_create.email}")
        #Hash password first
        try:
            password_hash = get_password_hash(user_create.password)
        except Exception as hash_err:
            log_status(logger, logging.INFO, f"Password hashing failed for user: {user_create.email}")
            log_exception_to_db(
                session=session,
                exc=str(hash_err),
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                endpoint="create_user/get_password_hash",
                module=__name__,
            )
            session.rollback()
            raise GenericException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password hashing failed."
            )
        
        # Validate and create User instance without password
        user_data = user_create.model_dump(exclude={"password"})
        user = User.model_validate(user_data)
        session.add(user)
        session.flush()

        # Create AuthCredential with hashed password linked to User
        auth_cred = AuthCredential(
            user_id=user.id,
            provider="local",
            password_hash=password_hash,
        )
        session.add(auth_cred)
        session.add(UserTenant(user_id=user.id, tenant_id=tenant_id))
        session.commit()
        log_status(logger, logging.INFO, f"User created successfully: {user.email}")
        return user

    except GenericException as ge:
        raise ge

    except SQLAlchemyError as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=str(e),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="create_user",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while creating user."
        )

    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=str(e),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="create_user",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error occurred while creating user."
        )


def update_user(*, session: Session, db_user: User, user_in: UserUpdate) -> Any:
    user_data = user_in.model_dump(exclude_unset=True)
    extra_data = {}
    if "password" in user_data:
        password = user_data["password"]
        hashed_password = get_password_hash(password)
        extra_data["hashed_password"] = hashed_password
    db_user.sqlmodel_update(user_data, update=extra_data)
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    return db_user


def get_user_by_email(*, session: Session, email: str) -> User | None:
    try:
        log_status(logger, logging.INFO, f"Attempting to retrieve user by email: {email}")
        statement = select(User).where(User.email == email)
        session_user = session.exec(statement).first()
        if session_user:
            log_status(logger, logging.INFO, f"User found for email: {email}")
        else:
            log_status(logger, logging.WARNING, f"No user found for email: {email}")
        return session_user
        
    except SQLAlchemyError as e:
        session.rollback()
        log_args = {
            "session": session,
            "endpoint": "get_user_by_email",
            "exc": str(e),
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "get_user_by_email",
            "module": __name__,
        }
        log_exception_to_db(**log_args)

        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving user by email."
        )

    except Exception as e:
        session.rollback()
        log_args = {
            "session": session,
            "endpoint": "get_user_by_email",
            "exc": str(e),
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "get_user_by_email",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error occurred while retrieving user by email."
        )



def authenticate(*, session: Session, email: str, password: str) -> User | None:
    try:
        log_status(logger, logging.INFO, f"Authentication attempt for email: {email}")
        db_user = get_user_by_email(session=session, email=email)
        if not db_user:
            log_status(logger, logging.WARNING, f"Authentication failed: user not found for email {email}")
            return None
        # Get associated AuthCredential for local provider
        auth_cred = session.exec(
            select(AuthCredential).where(
                AuthCredential.user_id == db_user.id,
                AuthCredential.provider == "local"
            )
        ).first()

        if not auth_cred or not auth_cred.password_hash:
            log_status(logger, logging.WARNING, f"Authentication failed: no credentials for user {email}")
            return None

        try:
            # Verify password
            if not verify_password(password, auth_cred.password_hash):
                log_status(logger, logging.WARNING, f"Authentication failed: invalid password for user {email}")
                return None
        
        except Exception as e:
            session.rollback()
            logger.error(f"Password verification failed for user {email}: {str(e)}", exc_info=True)
            # Log the password verification failure
            log_exception_to_db(
                session=session,
                exc=str(e),
                status_code=http_status.HTTP_401_UNAUTHORIZED,
                endpoint="verify_password",
                module=__name__,
            )
            raise GenericException(
                status_code=http_status.HTTP_401_UNAUTHORIZED,
                detail="Password verification failed"
            )

        log_status(logger, logging.INFO, f"Authentication successful for user {email}")
        return db_user
    
    except GenericException as ge:
        logger.error(f"GenericException in authenticate: {ge.detail}", exc_info=True)
        raise ge
    
    except SQLAlchemyError as db_err:
        session.rollback()
        logger.error("Database error during authentication.", exc_info=True)
        log_exception_to_db(
            session=session,
            exc=str(db_err),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="authenticate",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error during authentication."
        )

    except Exception as e:
        session.rollback()
        logger.error("Unexpected error during authentication.", exc_info=True)
        log_exception_to_db(
            session=session,
            exc=str(e),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="authenticate",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error during authentication."
        )


def create_item(*, session: Session, item_in: ItemCreate, owner_id: uuid.UUID) -> Item:
    db_item = Item.model_validate(item_in, update={"owner_id": owner_id})
    session.add(db_item)
    session.commit()
    session.refresh(db_item)
    return db_item



def get_auth_cred_from_userid(*, session: Session, user_id: str) -> AuthCredential | None:
    try:
        log_status(logger, logging.INFO, f"Attempting to retrieve AuthCredential for user_id: {user_id}")
        statement = select(AuthCredential).where(AuthCredential.user_id == user_id, AuthCredential.provider == "local")
        statement = select(AuthCredential).where(AuthCredential.user_id == user_id, AuthCredential.provider=="local")
        session_user = session.exec(statement).first()
        if session_user:
            log_status(logger, logging.INFO, f"AuthCredential found for user_id: {user_id}")
        else:
            log_status(logger, logging.WARNING, f"No AuthCredential found for user_id: {user_id}")
        return session_user
        
    except SQLAlchemyError as e:
        session.rollback()
        log_args = {
            "session": session,
            "endpoint": "get_auth_cred_from_userid",
            "exc": str(e),
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            "module": __name__,
        }
        log_exception_to_db(**log_args)

        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving auth_cred by userid."
        )

    except Exception as e:
        session.rollback()
        log_args = {
            "session": session,
            "endpoint": "get_auth_cred_from_userid",
            "exc": str(e),
            "status_code": http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            "endpoint": "get_auth_cred_from_userid",
            "module": __name__,
        }
        log_exception_to_db(**log_args)
        raise GenericException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error occurred while retrieving authe_cred by userid."
        )
    

def get_or_create_sso_user(
    *,
    session: Session,
    email: str,
    name: str,
    provider: str,
    provider_user_id: str
) -> User:
    try:
        log_status(logger, logging.INFO, f"SSO login attempt for email: {email} with provider: {provider}")
        #Look up existing user
        user = get_user_by_email(session=session, email=email)
        if user:
            auth_cred = session.exec(
                select(AuthCredential).where(
                    AuthCredential.user_id == user.id,
                    AuthCredential.provider == provider
                )
            ).first()
            if auth_cred:
                #Check for at least one active tenant
                # tenant_exists = session.exec(
                #     select(UserTenant)
                #     .join(Tenant)
                #     .where(
                #         UserTenant.user_id == user.id,
                #         Tenant.is_active == True,
                #         Tenant.deleted_at.is_(None)  
                #     )
                # ).first()
                
                # if not tenant_exists:
                #     log_status(logger, logging.WARNING, f"Login failed: No active tenant for user {user.email}")
                #     return JSONResponse(
                #         status_code=403,
                #         content={"status": False, "detail": "No active tenant associated with this user"}
                #     ) 
                # Generate access and refresh tokens
                access_token, refresh_token = security.create_tokens(session=session, user_id=user.id)
                log_status(logger, logging.INFO, f"SSO login successful for user: {email} (provider: {provider})")
                return JSONResponse(
                    status_code=200,
                    content={
                        "status": True,
                        "message": "Login successful",
                        "access_token": access_token,
                        "refresh_token": refresh_token,
                        "token_type": "bearer",
                        "role_id": 1  
                    })
            else:
                log_status(logger, logging.WARNING, f"SSO login failed: email {email} linked to username and password account")
                return JSONResponse(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                content={
                    "status": False,
                    "message": "Login failed",
                    "detail": "Login is not permitted. This email is associated with a username and password account. Please use the username and password to log in."
                })
        if not user:
            log_status(logger, logging.INFO, f"No user found for SSO login, creating new user: {email}")
            tenant_code="ASL001"# Hardcoded default value
            tenant = session.exec(
                select(Tenant).where(Tenant.tenant_code == tenant_code)
            ).first()
            if not tenant:
                log_status(logger, logging.WARNING, f"Invalid tenant code for user: ({user_in.email})")
                return JSONResponse(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    content={"status": False, "message": "Invalid tenant code."}
                )
            
            user = User(
                email=email,
                name=name,
                is_active=True
            )
            session.add(user)

            credential = AuthCredential(
                provider=provider,
                provider_user_id=provider_user_id,
                user_id=user.id
            )
            session.add(credential)

            session.add(UserTenant(user_id=user.id, tenant_id=tenant.id))
            session.commit()
            # Generate access and refresh tokens
            access_token, refresh_token = security.create_tokens(session=session, user_id=user.id)
            logger.info(f"SSO user created and logged in: {email}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": True,
                    "message": "Login successful",
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "role_id": 1  
                })
    except GenericException as ge:
        raise ge

    except SQLAlchemyError as db_err:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=str(db_err),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="get_or_create_sso_user",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error during SSO login."
        )

    except Exception as e:
        session.rollback()
        log_exception_to_db(
            session=session,
            exc=str(e),
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            endpoint="get_or_create_sso_user",
            module=__name__,
        )
        raise GenericException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unexpected error during SSO login."
        )
