import sentry_sdk
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.routing import APIRoute
from starlette.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from app.exception_log import log_status
from app.api.main import api_router
from app.core.config import settings
from app.utils import preload_dictionary
from app.exceptions import (
    BadRequestException,
    GenericException,
    bad_request_exception_handler,
    generic_exception_handler,
)
from config import setup_logging

logger = setup_logging("fastapi_main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for FastAPI startup and shutdown with detailed logging"""
    import time
    import os
    import psutil

    startup_start = time.time()
    pid = os.getpid()
    process = psutil.Process(pid)
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB

    # Startup
    try:
        print(f"FASTAPI STARTUP STARTING - PID: {pid}")  # Force console output
        logger.info(f"FastAPI startup initiated - PID: {pid}, Initial memory: {initial_memory:.1f}MB")

        # Preload dictionary for instant first request
        dict_start = time.time()
        log_status(logger, logging.INFO, "🔍 Preloading dictionary for optimized performance...")
        preload_dictionary()
        dict_time = time.time() - dict_start
        dict_memory = process.memory_info().rss / 1024 / 1024  # MB
        log_status(logger, logging.INFO,
                   f"Dictionary preloaded in {dict_time:.3f}s, Memory usage: {dict_memory:.1f}MB "
                   f"(+{dict_memory-initial_memory:.1f}MB), PID: {pid}")

        # Initialize ML models for optimized performance
        model_start = time.time()
        log_status(logger, logging.INFO, "Initializing ML models for optimized performance...")
        from app.core.model_manager import initialize_models
        initialize_models()
        model_time = time.time() - model_start

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_startup_time = time.time() - startup_start

        log_status(logger, logging.INFO,
                   f"FastAPI startup completed in {total_startup_time:.3f}s - PID: {pid}, "
                   f"Memory usage: {final_memory:.1f}MB "
                   f"(+{final_memory-initial_memory:.1f}MB), "
                   f"Model initialization time: {model_time:.3f}s, "
                   f"Dictionary preload time: {dict_time:.3f}s")

    except Exception as e:
        error_time = time.time() - startup_start
        error_memory = process.memory_info().rss / 1024 / 1024  # MB
        log_status(logger, logging.ERROR,
                   f"FastAPI startup error after {error_time:.3f}s - PID: {pid}, "
                   f"Memory usage: {error_memory:.1f}MB, Error: {e}")
        log_status(logger, logging.ERROR,
                   "An error occurred during FastAPI startup. "
                   "Please check the logs for more details.")
        # Log the exception to the database
        # Don't raise exception to prevent FastAPI from failing to start

    yield

    # Shutdown
    shutdown_start = time.time()
    try:
        log_status(logger, logging.INFO, f"FastAPI shutdown initiated - PID: {pid}, ")
        from app.core.model_manager import get_model_manager
        model_manager = get_model_manager()
        model_manager.cleanup()

        shutdown_time = time.time() - shutdown_start
        final_shutdown_memory = process.memory_info().rss / 1024 / 1024  # MB
        log_status(logger, logging.INFO,
                   f"FastAPI shutdown completed in {shutdown_time:.3f}s - PID: {pid}, "
                   f"Final memory usage: {final_shutdown_memory:.1f}MB "
                   f"(+{final_shutdown_memory-initial_memory:.1f}MB)")
    except Exception as e:
        error_time = time.time() - shutdown_start
        log_status(logger, logging.ERROR,
                   f"FastAPI shutdown error after {error_time:.3f}s - PID: {pid}, "
                   f"Memory usage: {final_memory:.1f}MB, Error: {e}")

def custom_generate_unique_id(route: APIRoute) -> str:
    return f"{route.tags[0]}-{route.name}"


if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(dsn=str(settings.SENTRY_DSN), enable_tracing=True)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    generate_unique_id_function=custom_generate_unique_id,
    lifespan=lifespan,
)

# Set all CORS enabled origins
if settings.all_cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.all_cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)
app.add_exception_handler(GenericException, generic_exception_handler)
app.add_exception_handler(BadRequestException, bad_request_exception_handler)
