Table tenant {
  id uuid [pk]
  org_name text
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}


Table role {
  id uuid [pk]
  name text [unique]  // e.g., admin, default_user
  tenant_id uuid [ref: > tenant.id]
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}

Table privileges {
  id uuid [pk]
  tenant_id uuid [ref: > tenant.id]
  name text [unique, not null] 
  description text
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}


Table role_privileges {
  role_id uuid [pk, ref: > role.id]
  privilege_id uuid [pk, ref: > privileges.id]
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}


Table user {
  id uuid [pk]
  name text
  email text [unique]
  mobile text
  is_active Boolean
  is_superuser Boolean
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid
  updated_by uuid [ref: > user.id]

}

Enum auth_provider {
  local  // username/password
  google // Gmail
  apple  // Apple ID
}

Table auth_credential {
  id uuid [pk]
  user_id uuid [ref: > user.id, not null]
  provider auth_provider [not null, note: 'local, google, apple']
  provider_user_id text [not null, note: 'e.g. email, google sub, apple ID']
  password_hash text [note: 'Only for local auth']
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]


  Indexes {
    (provider, provider_user_id) [unique, name: 'uq_provider_user']
  }
}




Table user_tenant {
  user_id uuid [pk, ref: > user.id]
  tenant_id uuid [pk, ref: > tenant.id]
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}



Table user_role {
  id uuid [pk]
  user_id uuid [ref: > user.id]
  role_id uuid [ref: > role.id]
  created_at timestamp
  updated_at timestamp [default: `now()`]
  created_by uuid [ref: > user.id]
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}


Enum feature_type {
  video
  audio
  text
}

Table usage_tracking {
  user_id uuid [pk, ref: > user.id]
  feature feature_type
  usage_date date [not null]
  video_duration integer [note: 'Duration in seconds of video played']
  audio_duration integer [note: 'Duration in seconds of audio played']
  word_count integer [note: 'Number of words processed (text feature)']
  start_time timestamp
  end_time timestamp
  metadata jsonb


  }


Table video_conversion {
  id serial [pk]
  user_id uuid [ref: > user.id]
  youtube_id varchar [not null]
  url varchar [not null]
  status statusenum [not null]
  created_at timestamptz [default: `now()`]
  modified_at timestamptz
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}

Table audio_conversion {
  id serial [pk]
  user_id uuid [ref: > user.id]
  status statusenum [not null]
  created_at timestamptz [default: `now()`]
  modified_at timestamptz
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}

Table transcriptions {
  id serial [pk]
  user_id uuid [ref: > user.id]
  video_id integer [not null, ref: > video_conversion.id] //on_delete: cascade]
  audio_id integer [not null, ref: > audio_conversion.id]
  start_time float8 [not null]
  end_time float8 [not null]
  word varchar [not null]
  root varchar [not null]
  chunk integer [not null]
  status statusenum [not null]
  created_at timestamptz [default: `now()`]
  modified_at timestamptz
  updated_by uuid [ref: > user.id]
  deleted_at timestamp
  deleted_by uuid [ref: > user.id]

}

Table exception_log {
  id int [pk, increment]
  exception_type exception_type [not null, note: 'Type of exception: database, fastapi']
  message text [not null, note: 'Summary of the error message']
  stack_trace text [note: 'Full stack trace or debug information']
  module varchar [note: 'Module or part of the application']
  endpoint varchar [note: 'URL path or function name (if backend)']
  file_name varchar [note: 'Filename where the error originated']
  user_id uuid [ref: > user.id] 
  created_at timestamp [not null, default: `now()`, note: 'Timestamp of the exception']
}