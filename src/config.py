import os
import shutil
import logging

import speech_recognition

import libqutrub.classverb as classverb
import libqutrub.verb_const as vconst

from pyarabic.araby import strip_harakat
from app.core.config import settings,TEMP_DIR


TEMP_DIRE = os.environ.get("TEMP_DIR", "/tmp/audio_uploads")

# from dotenv import dotenv_values
ARABIC_DICTIONARY_PATH = settings.DICTIONARY_FILE_PATH

def setup_logging(name):
    LOG_DIR = os.path.join(os.getcwd(), "logs")
    os.makedirs(LOG_DIR, exist_ok=True)
 
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
 
    if not logger.handlers:
        formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
 
        file_path = os.path.join(LOG_DIR, f"{name}_app.log")
        file_handler = logging.FileHandler(file_path, encoding="utf-8", mode="a")
        file_handler.setLevel(logging.INFO)  # ✅ FIXED LEVEL HERE
        file_handler.setFormatter(formatter)
 
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
 
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
 
    return logger


def setup_app():
    try:
        shutil.rmtree(TEMP_DIR)
    except Exception:
        pass
    os.makedirs(TEMP_DIR, exist_ok=True)


def get_verb_variants(verb):
    try:
        vbc = classverb.VerbClass(verb, True)
        present_form = strip_harakat(
            vbc.conjugate_tense_pronoun(vconst.TenseFuture, vconst.PronounHuwa)
        )
        past_form = strip_harakat(
            vbc.conjugate_tense_pronoun(vconst.TensePast, vconst.PronounHuwa)
        )
        return past_form, present_form
    except:
        return None, None


def read_dictionary():
    dictionary = {}
    verb_variants = {}
    file_path = ARABIC_DICTIONARY_PATH
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            parts = line.split(":")
            if len(parts) == 2:
                word, synonyms = line.strip().split(":")
                if dictionary.get(word.strip(), False):

                    dictionary[word.strip()].extend(
                        [syn.strip() for syn in synonyms.split("،")]
                    )
                else:
                    dictionary[word.strip()] = [
                        syn.strip() for syn in synonyms.split("،")
                    ]

                for verb in [word.strip()] + [
                    syn.strip() for syn in synonyms.split("،")
                ]:
                    past_form, present_form = get_verb_variants(verb)
                    if past_form:
                        verb_variants[past_form] = verb
                    if present_form:
                        verb_variants[present_form] = verb

    return dictionary, verb_variants


dictionary, verb_variants = read_dictionary()

speech_recognizer = speech_recognition.Recognizer()
