[project]
name = "app"
version = "0.1.0"
description = ""
requires-python = ">=3.10,<4.0"
dependencies = [
    "fastapi[standard]<1.0.0,>=0.114.2",
    "python-multipart<1.0.0,>=0.0.7",
    "email-validator<*******,>=2.1.0.post1",
    "passlib[bcrypt]<2.0.0,>=1.7.4",
    "tenacity<9.0.0,>=8.2.3",
    "pydantic>2.0",
    "emails<1.0,>=0.6",
    "jinja2<4.0.0,>=3.1.4",
    "alembic<2.0.0,>=1.12.1",
    "httpx<1.0.0,>=0.25.1",
    "psycopg[binary]<4.0.0,>=3.1.13",
    "sqlmodel<1.0.0,>=0.0.21",
    "bcrypt==4.0.1",  # Pin bcrypt until passlib supports the latest
    "pydantic-settings<3.0.0,>=2.2.1",
    "sentry-sdk[fastapi]<2.0.0,>=1.40.6",
    "pyjwt<3.0.0,>=2.8.0",
    "openai>=1.33.0,<2.0.0",
    "pytube>=15.0.0,<16.0.0",
    "pydub>=0.25.1,<1.0.0",
    "python-dotenv>=1.0.1,<2.0.0",
    "sqlalchemy>=2.0.30,<3.0.0",
    "tashaphyne>=0.3.6,<1.0.0",
    "websockets>=12.0,<13.0.0",
    "numpy==1.26.4",
    "yt-dlp>=2024.7.16,<2025.0.0",
    "libqutrub>=*******,<2.0.0",
    "speechrecognition>=3.10,<4.0.0",
    "torch>=2.7.1",
    "torchaudio>=2.7.1",
    "soundfile>=0.13.1",
    "gunicorn>=23.0.0",
    "psycopg2-binary>=2.9.10",
    "onnxruntime>=1.22.0",
    "silero-vad>=5.1.2",
    "celery>=5.5.3",
    "youtube_transcript_api>=1.1.0",
    "authlib>=1.3.0,<2.0.0",
    "python-jose[cryptography]>=3.3.0,<4.0.0",
    "google-auth>=2.0.0,<3.0.0",
    "google-auth-oauthlib>=1.0.0,<2.0.0",
    "itsdangerous>=2.1.0,<3.0.0",
    "uvicorn[standard]>=0.29.0,<1.0.0",
    "moviepy>=2.2.1",
    "firebase-admin>=6.9.0",
    "python-magic>=0.4.27,<1.0.0",
    "psutil>=5.8.0"
]


    

    [tool.uv]
dev-dependencies = [
    "pytest<8.0.0,>=7.4.3",
    "mypy<2.0.0,>=1.8.0",
    "ruff<1.0.0,>=0.2.2",
    "pre-commit<4.0.0,>=3.6.2",
    "types-passlib<*******,>=1.7.7.20240106",
    "coverage<8.0.0,>=7.4.3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true
